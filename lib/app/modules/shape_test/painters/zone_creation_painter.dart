import 'package:flutter/material.dart';
import 'package:xoxknit/app/modules/shape_test/models/cell_dimensions.dart';
import 'package:xoxknit/app/modules/shape_test/controllers/managers/knitting_instructions_manager.dart';

class ZoneCreationPainter extends CustomPainter {
  final List<List<bool>> fullPattern;
  final List<List<bool>> selectionGrid;
  final double aspectRatio;
  final List<Offset> polygonVertices;
  final bool isPolygonClosed;
  final CellDimensions cellDimensions;
  final List<KnittingZone> existingZones;

  // Alignment indicators
  final int? horizontalAlignedIndex;
  final int? verticalAlignedIndex;
  final double? alignmentPosition;
  final bool isHorizontalAlignment;

  // Zone colors with reduced opacity for background display
  final List<Color> zoneColors = [
    Colors.blue.withValues(alpha: .2),
    Colors.green.withValues(alpha: .2),
    Colors.orange.withValues(alpha: .2),
    Colors.purple.withValues(alpha: .2),
    Colors.teal.withValues(alpha: .2),
    Colors.red.withValues(alpha: .2),
    Colors.amber.withValues(alpha: .2),
    Colors.indigo.withValues(alpha: .2),
  ];

  ZoneCreationPainter({
    required this.fullPattern,
    required this.selectionGrid,
    this.aspectRatio = 0.75,
    required this.polygonVertices,
    this.isPolygonClosed = false,
    required this.cellDimensions,
    this.existingZones = const [],
    this.horizontalAlignedIndex,
    this.verticalAlignedIndex,
    this.alignmentPosition,
    this.isHorizontalAlignment = false,
  });

  @override
  void paint(Canvas canvas, Size size) {
    if (fullPattern.isEmpty) return;

    final patternWidth = fullPattern[0].length;
    final patternHeight = fullPattern.length;

    final cellWidth = cellDimensions.cellWidth;
    final cellHeight = cellDimensions.cellHeight;
    final offsetX = cellDimensions.offsetX;
    final offsetY = cellDimensions.offsetY;

    // Draw grid
    final gridPaint = Paint()
      ..color = Colors.grey.shade300
      ..style = PaintingStyle.stroke
      ..strokeWidth = 0.5;

    // Draw vertical grid lines
    for (int col = 0; col <= patternWidth; col++) {
      final x = offsetX + (col * cellWidth);
      canvas.drawLine(Offset(x, offsetY),
          Offset(x, offsetY + (patternHeight * cellHeight)), gridPaint);
    }

    // Draw horizontal grid lines
    for (int row = 0; row <= patternHeight; row++) {
      final y = offsetY + (row * cellHeight);
      canvas.drawLine(Offset(offsetX, y),
          Offset(offsetX + (patternWidth * cellWidth), y), gridPaint);
    }

    // Set up stitch paint
    final stitchPaint = Paint()
      ..color = Colors.black.withOpacity(0.15)
      ..style = PaintingStyle.fill;

    // Draw background stitches first (the base pattern)
    for (int row = 0; row < patternHeight; row++) {
      for (int col = 0; col < patternWidth; col++) {
        if (fullPattern[row][col]) {
          final rect = Rect.fromLTWH(offsetX + (col * cellWidth),
              offsetY + (row * cellHeight), cellWidth, cellHeight);
          canvas.drawRect(rect, stitchPaint);
        }
      }
    }

    // Draw existing zones in the background with reduced opacity
    if (existingZones.isNotEmpty) {
      for (int i = 0; i < existingZones.length; i++) {
        final zone = existingZones[i];
        final instructions = zone.instructions;

        // Skip empty zones
        if (instructions.isEmpty) continue;

        // Set zone paint color with low opacity for background visibility
        final zonePaint = Paint()
          ..color = zoneColors[i % zoneColors.length]
          ..style = PaintingStyle.fill;

        // Draw the zone's stitches
        for (int localRow = 0; localRow < instructions.length; localRow++) {
          final globalRowIndex = zone.startRow + localRow;
          final currentRowData = instructions[localRow];

          for (int localCol = 0; localCol < currentRowData.length; localCol++) {
            if (currentRowData[localCol]) {
              // Convert local column to global column
              final globalColIndex = zone.startNeedle + localCol;

              final rect = Rect.fromLTWH(
                  offsetX + (globalColIndex * cellWidth),
                  offsetY + (globalRowIndex * cellHeight),
                  cellWidth,
                  cellHeight);
              canvas.drawRect(rect, zonePaint);
            }
          }
        }

        // Draw zone outline with dashed lines
        final outlinePaint = Paint()
          ..color = zoneColors[i % zoneColors.length].withOpacity(0.6)
          ..style = PaintingStyle.stroke
          ..strokeWidth = 1.0;

        final rect = Rect.fromLTWH(
            offsetX + (zone.startNeedle * cellWidth),
            offsetY + (zone.startRow * cellHeight),
            (zone.endNeedle - zone.startNeedle + 1) * cellWidth,
            (zone.endRow - zone.startRow + 1) * cellHeight);

        canvas.drawRect(rect, outlinePaint);

        // Draw zone number as text in the center
        // Calculate the zone's bounding rectangle
        final zoneRect = Rect.fromLTWH(
            offsetX + (zone.startNeedle * cellWidth),
            offsetY + (zone.startRow * cellHeight),
            (zone.endNeedle - zone.startNeedle + 1) * cellWidth,
            (zone.endRow - zone.startRow + 1) * cellHeight);

        // Create a background for better visibility
        final labelBgPaint = Paint()
          ..color = Colors.white.withValues(alpha: 0.2)
          ..style = PaintingStyle.fill;

        final textStyle = TextStyle(
          color: Colors.black.withValues(alpha: 0.7),
          fontSize: 12,
          fontWeight: FontWeight.normal,
        );

        final textSpan = TextSpan(
          text: zone.name,
          style: textStyle,
        );

        final textPainter = TextPainter(
          text: textSpan,
          textDirection: TextDirection.ltr,
          textAlign: TextAlign.center,
        );

        textPainter.layout();

        // Calculate center position of the zone
        final centerX = zoneRect.left + zoneRect.width / 2;
        final centerY = zoneRect.top + zoneRect.height / 2;

        // Draw text background
        final padding = 4.0;
        final bgRect = Rect.fromCenter(
          center: Offset(centerX, centerY),
          width: textPainter.width + padding * 2,
          height: textPainter.height + padding * 2,
        );

        // Draw the background with rounded corners
        canvas.drawRRect(
          RRect.fromRectAndRadius(bgRect, Radius.circular(4.0)),
          labelBgPaint,
        );

        // Draw the text in the center of the zone
        textPainter.paint(
          canvas,
          Offset(
            centerX - textPainter.width / 2,
            centerY - textPainter.height / 2,
          ),
        );
      }
    }

    // Draw selected stitches
    final selectionPaint = Paint()
      ..color = Colors.blue.withOpacity(0.6)
      ..style = PaintingStyle.fill;

    // Draw each selected stitch
    for (int row = 0; row < selectionGrid.length; row++) {
      for (int col = 0; col < selectionGrid[row].length; col++) {
        if (selectionGrid[row][col]) {
          final rect = Rect.fromLTWH(offsetX + (col * cellWidth),
              offsetY + (row * cellHeight), cellWidth, cellHeight);
          canvas.drawRect(rect, selectionPaint);
        }
      }
    }

    // Draw alignment indicators if active
    if (alignmentPosition != null && polygonVertices.isNotEmpty) {
      final alignmentPaint = Paint()
        ..color = Colors.green.shade500
        ..style = PaintingStyle.stroke
        ..strokeWidth = 1.5;

      // Draw dashed line for the alignment indicator
      final dashWidth = 5.0;
      final dashSpace = 5.0;
      double startPos = 0;

      if (isHorizontalAlignment) {
        // Horizontal alignment - draw a horizontal line
        final double y = alignmentPosition!;

        // Draw a dashed horizontal line across the entire width
        double x = offsetX;
        final double endX = offsetX + patternWidth * cellWidth;

        // Draw a dashed line
        while (x < endX) {
          // Draw a dash
          canvas.drawLine(
              Offset(x, y),
              Offset(x + dashWidth < endX ? x + dashWidth : endX, y),
              alignmentPaint);
          // Move position for space
          x += dashWidth + dashSpace;
        }

        // Draw alignment point indicators
        if (horizontalAlignedIndex != null &&
            horizontalAlignedIndex! < polygonVertices.length) {
          // Draw circle around the aligned point
          final alignedPoint = polygonVertices[horizontalAlignedIndex!];

          // Draw an outlined circle to highlight the aligned point
          canvas.drawCircle(
              alignedPoint,
              10.0,
              Paint()
                ..color = Colors.green.shade200.withOpacity(0.5)
                ..style = PaintingStyle.fill);

          canvas.drawCircle(
              alignedPoint,
              10.0,
              Paint()
                ..color = Colors.green
                ..style = PaintingStyle.stroke
                ..strokeWidth = 1.5);
        }
      } else {
        // Vertical alignment - draw a vertical line
        final double x = alignmentPosition!;

        // Draw a dashed vertical line across the entire height
        double y = offsetY;
        final double endY = offsetY + patternHeight * cellHeight;

        // Draw a dashed line
        while (y < endY) {
          // Draw a dash
          canvas.drawLine(
              Offset(x, y),
              Offset(x, y + dashWidth < endY ? y + dashWidth : endY),
              alignmentPaint);
          // Move position for space
          y += dashWidth + dashSpace;
        }

        // Draw alignment point indicators
        if (verticalAlignedIndex != null &&
            verticalAlignedIndex! < polygonVertices.length) {
          // Draw circle around the aligned point
          final alignedPoint = polygonVertices[verticalAlignedIndex!];

          // Draw an outlined circle to highlight the aligned point
          canvas.drawCircle(
              alignedPoint,
              10.0,
              Paint()
                ..color = Colors.green.shade200.withOpacity(0.5)
                ..style = PaintingStyle.fill);

          canvas.drawCircle(
              alignedPoint,
              10.0,
              Paint()
                ..color = Colors.green
                ..style = PaintingStyle.stroke
                ..strokeWidth = 1.5);
        }
      }
    }

    // Draw polygon selection - MAKE POINTS MORE VISIBLE
    if (polygonVertices.isNotEmpty) {
      // Print vertices information for debugging
      debugPrint(
          'Drawing ${polygonVertices.length} vertices: $polygonVertices');

      // Draw lines connecting vertices - thicker and more visible
      final polygonPaint = Paint()
        ..color = Colors.blue
        ..style = PaintingStyle.stroke
        ..strokeWidth = 3.0; // Increased thickness

      // Create the polygon path
      final path = Path();
      path.moveTo(polygonVertices[0].dx, polygonVertices[0].dy);

      for (int i = 1; i < polygonVertices.length; i++) {
        path.lineTo(polygonVertices[i].dx, polygonVertices[i].dy);
      }

      // Close the path if needed
      if (isPolygonClosed && polygonVertices.length > 2) {
        path.close();
      }

      canvas.drawPath(path, polygonPaint);

      // Draw vertices with larger, more visible dots
      for (int i = 0; i < polygonVertices.length; i++) {
        // First vertex (starting point)
        if (i == 0) {
          // Draw a larger, more noticeable first point
          canvas.drawCircle(
              polygonVertices[i],
              8.0, // Larger radius
              Paint()
                ..color = Colors.red.shade700 // More vibrant color
                ..style = PaintingStyle.fill);

          // Add a white outline to make it stand out more
          canvas.drawCircle(
              polygonVertices[i],
              8.0,
              Paint()
                ..color = Colors.white
                ..style = PaintingStyle.stroke
                ..strokeWidth = 2.0);
        } else {
          // Draw regular vertices with larger, more visible dots
          canvas.drawCircle(
              polygonVertices[i],
              6.0, // Increased size
              Paint()
                ..color = Colors.blue.shade600 // More vibrant blue
                ..style = PaintingStyle.fill);

          // Add white outline for better visibility
          canvas.drawCircle(
              polygonVertices[i],
              6.0,
              Paint()
                ..color = Colors.white
                ..style = PaintingStyle.stroke
                ..strokeWidth = 1.5);
        }
      }

      // Draw fill if polygon is valid for selection (3+ points)
      if (polygonVertices.length >= 3) {
        final fillPath = Path();
        fillPath.moveTo(polygonVertices[0].dx, polygonVertices[0].dy);

        for (int i = 1; i < polygonVertices.length; i++) {
          fillPath.lineTo(polygonVertices[i].dx, polygonVertices[i].dy);
        }

        fillPath.close();

        final fillPaint = Paint()
          ..color = Colors.blue.withOpacity(0.25) // Slightly more opaque
          ..style = PaintingStyle.fill;

        canvas.drawPath(fillPath, fillPaint);
      }
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    if (oldDelegate is ZoneCreationPainter) {
      return oldDelegate.fullPattern != fullPattern ||
          oldDelegate.selectionGrid != selectionGrid ||
          oldDelegate.aspectRatio != aspectRatio ||
          oldDelegate.polygonVertices != polygonVertices ||
          oldDelegate.isPolygonClosed != isPolygonClosed ||
          oldDelegate.existingZones != existingZones ||
          oldDelegate.horizontalAlignedIndex != horizontalAlignedIndex ||
          oldDelegate.verticalAlignedIndex != verticalAlignedIndex ||
          oldDelegate.alignmentPosition != alignmentPosition ||
          oldDelegate.isHorizontalAlignment != isHorizontalAlignment;
    }
    return true;
  }
}
