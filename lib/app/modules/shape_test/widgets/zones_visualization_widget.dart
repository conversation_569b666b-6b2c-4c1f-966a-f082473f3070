import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:xoxknit/app/modules/shape_test/controllers/managers/knitting_instructions_manager.dart';
import 'package:xoxknit/app/modules/shape_test/models/cell_dimensions.dart';
import 'package:xoxknit/app/modules/shape_test/painters/zones_pattern_painter.dart';
import 'package:xoxknit/app/modules/shape_test/widgets/zone_boundary_editor_widget.dart';
import 'package:xoxknit/app/modules/shape_test/controllers/zones_editor_controller.dart';

class ZonesVisualizationWidget extends StatelessWidget {
  final List<List<bool>> fullPattern;
  final List<KnittingZone> zones;
  final int selectedZoneIndex;
  final double aspectRatio;
  final bool isEditingMode;
  final Function(int)? onZoneTap;
  final Function(Offset tapPosition, Size canvasSize)? onZoneTapWithPosition;
  final Function(Offset tapPosition, Size canvasSize)? onZoneLongPress;
  final Function(int, String, int)? onZoneBoundaryEdit;
  final ZonesEditorController? controller; // Optional controller for updates

  const ZonesVisualizationWidget({
    super.key,
    required this.fullPattern,
    required this.zones,
    required this.selectedZoneIndex,
    this.aspectRatio = 0.75,
    this.isEditingMode = false,
    this.onZoneTap,
    this.onZoneTapWithPosition,
    this.onZoneLongPress,
    this.onZoneBoundaryEdit,
    this.controller,
  });

  @override
  Widget build(BuildContext context) {
    if (fullPattern.isEmpty) {
      return const SizedBox();
    }

    // If a controller is provided, use GetBuilder to listen for updates
    if (controller != null) {
      return GetBuilder<ZonesEditorController>(
        init: controller,
        builder: (_) => _buildVisualization(context),
      );
    }

    // Otherwise, build directly
    return _buildVisualization(context);
  }

  Widget _buildVisualization(BuildContext context) {
    // Use the controller's selectedZoneIndex if available, otherwise use the parameter
    final actualSelectedZoneIndex =
        controller?.selectedZoneIndex.value ?? selectedZoneIndex;

    Widget childWidget;
    if (isEditingMode && onZoneBoundaryEdit != null) {
      debugPrint(
          'ZonesVisualization: Using ZoneBoundaryEditorWidget (isEditingMode=$isEditingMode, onZoneBoundaryEdit=${onZoneBoundaryEdit != null ? 'non-null' : 'null'}, selectedZoneIndex=$actualSelectedZoneIndex)');
      childWidget = ZoneBoundaryEditorWidget(
        fullPattern: fullPattern,
        zones: zones,
        selectedZoneIndex: actualSelectedZoneIndex,
        aspectRatio: aspectRatio,
        onZoneTap: onZoneTap,
        onZoneTapWithPosition: onZoneTapWithPosition,
        onZoneLongPress: onZoneLongPress,
        onZoneBoundaryEdit: onZoneBoundaryEdit,
        controller: controller,
      );
    } else {
      debugPrint(
          'ZonesVisualization: Using regular GestureDetector (isEditingMode=$isEditingMode, onZoneBoundaryEdit=${onZoneBoundaryEdit != null ? 'non-null' : 'null'})');
      childWidget = GestureDetector(
        behavior: HitTestBehavior.opaque,
        onTapUp: (details) {
          final RenderBox box = context.findRenderObject() as RenderBox;
          final localPos = box.globalToLocal(details.globalPosition);
          final Size size = box.size;

          // Check if tap is inside the context menu first
          if (controller?.isTapInsideZoneContextMenu(details.globalPosition) ==
              true) {
            return; // Let the menu handle it
          }

          // Hide context menu if visible and tap is outside
          if (controller?.isZoneContextMenuVisible.value == true) {
            controller?.hideZoneContextMenu();
          }

          // Use the advanced overlap detection if available
          if (onZoneTapWithPosition != null) {
            onZoneTapWithPosition!(localPos, size);
            return;
          }

          // Fallback to simple detection
          if (onZoneTap == null) return;

          // Calculate cell dimensions
          final cellDimensions = CellDimensions.calculate(
              size, fullPattern[0].length, fullPattern.length, aspectRatio);

          // Find which zone was tapped
          for (int i = 0; i < zones.length; i++) {
            final zone = zones[i];

            // Calculate zone boundaries
            final left = zone.startNeedle * cellDimensions.cellWidth +
                cellDimensions.offsetX;
            final top = zone.startRow * cellDimensions.cellHeight +
                cellDimensions.offsetY;
            final right = (zone.endNeedle + 1) * cellDimensions.cellWidth +
                cellDimensions.offsetX;
            final bottom = (zone.endRow + 1) * cellDimensions.cellHeight +
                cellDimensions.offsetY;

            // Check if tap is within zone boundaries
            if (localPos.dx >= left &&
                localPos.dx <= right &&
                localPos.dy >= top &&
                localPos.dy <= bottom) {
              onZoneTap!(i);
              break;
            }
          }
        },
        onLongPressStart: onZoneLongPress != null
            ? (details) {
                final RenderBox box = context.findRenderObject() as RenderBox;
                final localPos = box.globalToLocal(details.globalPosition);
                final Size size = box.size;

                onZoneLongPress!(localPos, size);
              }
            : null,
        child: Container(
          color: Colors.transparent,
          width: double.infinity,
          height: double.infinity,
        ),
      );
    }

    return CustomPaint(
      painter: ZonesPatternPainter(
        fullPattern: fullPattern,
        zones: zones,
        selectedZoneIndex: actualSelectedZoneIndex,
        aspectRatio: aspectRatio,
      ),
      child: childWidget,
    );
  }
}
