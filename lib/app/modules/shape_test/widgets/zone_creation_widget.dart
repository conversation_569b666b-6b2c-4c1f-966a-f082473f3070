import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:xoxknit/app/modules/shape_test/controllers/zone_creation_controller.dart';
import 'package:xoxknit/app/modules/shape_test/models/cell_dimensions.dart';
import 'package:xoxknit/app/modules/shape_test/painters/zone_creation_painter.dart';
import 'package:xoxknit/app/modules/shape_test/controllers/managers/knitting_instructions_manager.dart';

class ZoneCreationWidget extends StatefulWidget {
  final List<List<bool>> fullPattern;
  final List<List<bool>> selectionGrid;
  final double aspectRatio;
  final Function(int, int, bool) onSelectionChanged;
  final ZoneCreationController controller;
  final List<KnittingZone> existingZones;
  final bool allowZoomGestures;

  const ZoneCreationWidget({
    super.key,
    required this.fullPattern,
    required this.selectionGrid,
    this.aspectRatio = 0.75,
    required this.onSelectionChanged,
    required this.controller,
    this.existingZones = const [],
    this.allowZoomGestures = false,
  });

  @override
  State<ZoneCreationWidget> createState() => _ZoneCreationWidgetState();
}

class _ZoneCreationWidgetState extends State<ZoneCreationWidget> {
  late CellDimensions _cellDimensions;
  bool _isMultiTouch = false;
  int _activePointerCount = 0;

  @override
  void initState() {
    super.initState();
    // Set up a listener to force rebuild when vertices change
    _setupListeners();
  }

  void _setupListeners() {
    // Listen to changes in polygon vertices
    widget.controller.polygonVertices.listen((_) {
      if (mounted) {
        setState(() {});
      }
    });

    // Listen to polygon closed state
    widget.controller.isPolygonClosed.listen((_) {
      if (mounted) {
        setState(() {});
      }
    });

    // Listen to dragged point index changes
    widget.controller.draggedPointIndex.listen((_) {
      if (mounted) {
        setState(() {});
      }
    });

    // Listen to alignment indicators changes
    widget.controller.horizontalAlignedIndex.listen((_) {
      if (mounted) {
        setState(() {});
      }
    });

    widget.controller.verticalAlignedIndex.listen((_) {
      if (mounted) {
        setState(() {});
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    if (widget.fullPattern.isEmpty ||
        widget.selectionGrid.isEmpty ||
        (widget.selectionGrid.isNotEmpty &&
            widget.selectionGrid[0].isEmpty &&
            widget.fullPattern.isNotEmpty &&
            widget.fullPattern[0].isNotEmpty)) {
      return const Center(
          child: Text("Pattern or selection grid not available or invalid."));
    }

    // Print debug information when widget builds
    debugPrint(
        'ZoneCreationWidget build - Polygon has ${widget.controller.polygonVertices.length} vertices');

    return LayoutBuilder(
      builder: (context, constraints) {
        final size = Size(constraints.maxWidth, constraints.maxHeight);
        final double correctedAspectRatio =
            widget.aspectRatio != 0 ? 1.0 / widget.aspectRatio : 1.0;
        _cellDimensions = CellDimensions.calculate(
            size,
            widget.fullPattern[0].length,
            widget.fullPattern.length,
            correctedAspectRatio);

        return Stack(
          children: [
            // Base layer: CustomPaint for visualization
            CustomPaint(
              painter: ZoneCreationPainter(
                fullPattern: widget.fullPattern,
                selectionGrid: widget.selectionGrid,
                aspectRatio: widget.aspectRatio,
                polygonVertices: widget.controller.polygonVertices,
                isPolygonClosed: widget.controller.isPolygonClosed.value,
                cellDimensions: _cellDimensions,
                existingZones: widget.existingZones,
                horizontalAlignedIndex:
                    widget.controller.horizontalAlignedIndex.value,
                verticalAlignedIndex:
                    widget.controller.verticalAlignedIndex.value,
                alignmentPosition: widget.controller.alignmentPosition.value,
                isHorizontalAlignment:
                    widget.controller.isHorizontalAlignment.value,
              ),
              size: size,
            ),

            // Add a Listener to track multi-touch when zooming is enabled
            if (widget.allowZoomGestures)
              Listener(
                onPointerDown: (_) {
                  _activePointerCount++;
                  _updateMultiTouchState();
                },
                onPointerUp: (_) {
                  _activePointerCount--;
                  _updateMultiTouchState();
                },
                onPointerCancel: (_) {
                  _activePointerCount--;
                  _updateMultiTouchState();
                },
                behavior: HitTestBehavior.translucent,
                child: Container(
                  width: double.infinity,
                  height: double.infinity,
                  color: Colors.transparent,
                ),
              ),

            // Second layer: Transparent GestureDetector for interactions
            GestureDetector(
              onTapDown: (TapDownDetails details) {
                // Skip gesture handling for multi-touch when zooming is enabled
                if (widget.allowZoomGestures && _isMultiTouch) return;

                // Don't allow adding new vertices if polygon is closed
                if (widget.controller.isPolygonClosed.value) return;

                // Check if we should close the polygon by tapping on the first point
                final draggedIndex =
                    widget.controller.polygonVertices.isNotEmpty
                        ? _getNearestVertexIndex(details.localPosition, 15.0)
                        : null;

                if (draggedIndex == 0 &&
                    widget.controller.polygonVertices.length >= 3) {
                  widget.controller.closePolygon();
                  widget.controller.applyPolygonSelection(
                      widget.fullPattern,
                      _cellDimensions,
                      Size(constraints.maxWidth, constraints.maxHeight));
                  return;
                }

                // Otherwise add a new vertex
                widget.controller.addVertex(details.localPosition);
              },
              onPanStart: (details) {
                // Skip gesture handling for multi-touch when zooming is enabled
                if (widget.allowZoomGestures && _isMultiTouch) return;

                // Check if we're starting to drag a vertex
                final draggedIndex = _getNearestVertexIndex(
                    details.localPosition, 15.0); // 15.0 is the touch radius
                if (draggedIndex != null) {
                  widget.controller.startDraggingPoint(draggedIndex);
                  setState(() {});
                }
              },
              onPanUpdate: (details) {
                // Skip gesture handling for multi-touch when zooming is enabled
                if (widget.allowZoomGestures && _isMultiTouch) return;

                if (widget.controller.draggedPointIndex.value != null) {
                  // Update vertex position
                  widget.controller.updateVertexPosition(
                      widget.controller.draggedPointIndex.value!,
                      details.localPosition);

                  // Check for double tap or key press to snap to alignment
                  if (widget.controller.alignmentPosition.value != null) {
                    // Automatically snap to alignment
                    widget.controller.snapToAlignment();
                  }
                }
              },
              onPanEnd: (details) {
                if (widget.controller.draggedPointIndex.value != null &&
                    widget.controller.isPolygonClosed.value) {
                  // If dragging ended by closing the polygon
                  widget.controller.applyPolygonSelection(
                      widget.fullPattern,
                      _cellDimensions,
                      Size(constraints.maxWidth, constraints.maxHeight));
                }
                widget.controller.stopDragging();
                setState(() {});
              },
              child: Container(
                color: Colors.transparent,
                width: double.infinity,
                height: double.infinity,
              ),
            ),

            // Point indicators - extra widget layer to make points more visible and draggable
            ...widget.controller.polygonVertices.asMap().entries.map((entry) {
              final index = entry.key;
              final vertex = entry.value;

              // Determine if this point is currently being dragged
              final isSelected =
                  widget.controller.draggedPointIndex.value == index;

              // Special styling for the first point (used to close the polygon)
              final isFirstPoint = index == 0;

              // Enhanced visibility for the point
              return Positioned(
                left: vertex.dx - 15, // Larger touch target
                top: vertex.dy - 15, // Larger touch target
                child: GestureDetector(
                  onPanStart: (details) {
                    // Skip gesture handling for multi-touch when zooming is enabled
                    if (widget.allowZoomGestures && _isMultiTouch) return;

                    widget.controller.startDraggingPoint(index);
                    setState(() {});
                  },
                  onTap: () {
                    // Skip gesture handling for multi-touch when zooming is enabled
                    if (widget.allowZoomGestures && _isMultiTouch) return;

                    // If this is the first point and we have enough vertices, close the polygon
                    if (index == 0 &&
                        widget.controller.polygonVertices.length >= 3 &&
                        !widget.controller.isPolygonClosed.value) {
                      widget.controller.closePolygon();
                      widget.controller.applyPolygonSelection(
                          widget.fullPattern,
                          _cellDimensions,
                          Size(constraints.maxWidth, constraints.maxHeight));
                    }
                  },
                  onPanUpdate: (details) {
                    // Skip gesture handling for multi-touch when zooming is enabled
                    if (widget.allowZoomGestures && _isMultiTouch) return;

                    final RenderBox box =
                        context.findRenderObject() as RenderBox;
                    final localPosition =
                        box.globalToLocal(details.globalPosition);
                    widget.controller
                        .updateVertexPosition(index, localPosition);

                    // Check for alignment and snap if needed
                    if (widget.controller.alignmentPosition.value != null) {
                      // Automatically snap to alignment
                      widget.controller.snapToAlignment();
                    }
                  },
                  onPanEnd: (details) {
                    if (widget.controller.isPolygonClosed.value) {
                      widget.controller.applyPolygonSelection(
                          widget.fullPattern,
                          _cellDimensions,
                          Size(constraints.maxWidth, constraints.maxHeight));
                    }
                    widget.controller.stopDragging();
                    setState(() {});
                  },
                  child: Container(
                    width: 30, // Larger touch target
                    height: 30, // Larger touch target
                    color: Colors.transparent, // Make the container invisible
                    child: Center(
                      child: AnimatedContainer(
                        duration: const Duration(milliseconds: 150),
                        width: isSelected ? 28 : 20,
                        height: isSelected ? 28 : 20,
                        decoration: BoxDecoration(
                          color: isFirstPoint ? Colors.red : Colors.blue,
                          borderRadius: BorderRadius.circular(15),
                          border: Border.all(
                            color: Colors.white,
                            width: isSelected ? 3 : 2,
                          ),
                          boxShadow: isSelected
                              ? [
                                  BoxShadow(
                                    color: Colors.black.withOpacity(0.3),
                                    blurRadius: 4,
                                    spreadRadius: 1,
                                  )
                                ]
                              : null,
                        ),
                        alignment: Alignment.center,
                        child: Text(
                          '${index + 1}',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: isSelected ? 14 : 10,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              );
            }),
          ],
        );
      },
    );
  }

  void _updateMultiTouchState() {
    setState(() {
      _isMultiTouch = _activePointerCount > 1; // Multi-touch means 2+ pointers
    });
  }

  // Check if a point is near any vertex
  int? _getNearestVertexIndex(Offset point, double threshold) {
    for (int i = 0; i < widget.controller.polygonVertices.length; i++) {
      final distance = (point - widget.controller.polygonVertices[i]).distance;
      if (distance < threshold) {
        return i;
      }
    }
    return null;
  }
}
