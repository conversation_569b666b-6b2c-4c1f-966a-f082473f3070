import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:xoxknit/app/modules/new_item/controllers/new_item_wizard_controller.dart';
import 'package:xoxknit/app/modules/new_item/utils/knitting_utils.dart';
import 'package:xoxknit/app/modules/shape_test/utils/knitting_instructions_generator_new.dart';
import '../../models/shape_data.dart';
import '../../utils/knitting_instructions_generator.dart';
import '../../utils/knitting_pattern_generator.dart';
import '../shape_editor_controller.dart';
import 'dart:math' as math;
import 'dart:ui' as ui;
import 'dart:convert' show base64, utf8;
import 'package:crypto/crypto.dart';

/// Manager class for handling knitting instruction generation
class KnittingInstructionsManager {
  // Reference to the parent controller
  final ShapeEditorController _controller;

  // Current instruction grid
  final Rx<List<List<bool>>> currentInstructions = Rx<List<List<bool>>>([]);

  final Rx<List<KnittingZone>> knittingZones = Rx<List<KnittingZone>>([]);

  // Statistics about the current pattern
  final Rx<Map<String, dynamic>> patternStatistics =
      Rx<Map<String, dynamic>>({});

  // Flag to indicate if instructions are being generated
  final RxBool isGeneratingInstructions = false.obs;

  // Pattern options - standardize on top-down only
  final RxBool startFromBottom =
      false.obs; // Set to false and keep for compatibility
  final RxBool includeRowNumbers = true.obs;

  // Zone history manager for undo/redo functionality
  final ZoneHistoryManager _zoneHistoryManager = ZoneHistoryManager();

  KnittingInstructionsManager(this._controller);

  // Getters for zone history functionality
  ZoneHistoryManager get zoneHistoryManager => _zoneHistoryManager;
  RxBool get canUndoZones => _zoneHistoryManager.canUndo;
  RxBool get canRedoZones => _zoneHistoryManager.canRedo;
  Rx<String?> get lastZoneOperation => _zoneHistoryManager.lastOperation;

  /// Calculate a hash of the instruction pattern for comparison
  String _calculateInstructionsHash(List<List<bool>> instructions) {
    if (instructions.isEmpty) return '';

    // Convert instructions to a string representation
    final buffer = StringBuffer();
    for (final row in instructions) {
      buffer.write(row.map((cell) => cell ? '1' : '0').join(''));
    }

    // Calculate MD5 hash
    final bytes = utf8.encode(buffer.toString());
    final digest = md5.convert(bytes);
    return digest.toString();
  }

  /// Load saved zones from wizard state if they match the current instructions
  Future<bool> _loadSavedZonesIfCompatible(
      List<List<bool>> newInstructions) async {
    try {
      if (!Get.isRegistered<NewItemWizardController>()) {
        return false;
      }

      final wizardController = Get.find<NewItemWizardController>();
      if (wizardController.currentStateId.value == null) {
        return false;
      }

      final state = await wizardController.wizardStateService
          .loadWizardState(wizardController.currentStateId.value!);

      if (state?.instructionsHash == null) {
        debugPrint('[KnittingInstructionsManager] No saved hash found');
        return false;
      }

      // Calculate hash of new instructions
      final newHash = _calculateInstructionsHash(newInstructions);

      // Check if instructions have changed
      if (state!.instructionsHash != newHash) {
        debugPrint(
            '[KnittingInstructionsManager] Instructions changed, regenerating zones');
        debugPrint('  Old hash: ${state.instructionsHash}');
        debugPrint('  New hash: $newHash');
        return false;
      }

      // Instructions match, try to load saved zones
      // First try to load full zones from local storage
      if (state.savedKnittingZones != null &&
          state.savedKnittingZones!.isNotEmpty) {
        debugPrint(
            '[KnittingInstructionsManager] Loading full zones from local storage (${state.savedKnittingZones!.length} zones)');

        // Verify zones are still valid for the current instructions
        if (_validateZonesAgainstInstructions(
            state.savedKnittingZones!, newInstructions)) {
          knittingZones.value =
              List<KnittingZone>.from(state.savedKnittingZones!);
          debugPrint(
              '[KnittingInstructionsManager] Loaded full zones are valid');
          return true;
        } else {
          debugPrint(
              '[KnittingInstructionsManager] Loaded full zones are invalid');
        }
      }

      // If full zones aren't available or invalid, try to reconstruct from metadata
      if (state.savedZoneMetadata != null &&
          state.savedZoneMetadata!.isNotEmpty) {
        debugPrint(
            '[KnittingInstructionsManager] Reconstructing zones from metadata (${state.savedZoneMetadata!.length} zones)');

        final reconstructedZones = _reconstructZonesFromMetadata(
            state.savedZoneMetadata!, newInstructions);
        if (reconstructedZones.isNotEmpty) {
          knittingZones.value = reconstructedZones;
          debugPrint(
              '[KnittingInstructionsManager] Successfully reconstructed zones from metadata');
          return true;
        } else {
          debugPrint(
              '[KnittingInstructionsManager] Failed to reconstruct zones from metadata');
        }
      }

      debugPrint('[KnittingInstructionsManager] No valid saved zones found');
      return false;
    } catch (e) {
      debugPrint('[KnittingInstructionsManager] Error loading saved zones: $e');
      return false;
    }
  }

  /// Reconstruct full zones from lightweight metadata and current instructions
  List<KnittingZone> _reconstructZonesFromMetadata(
      List<ZoneMetadata> metadata, List<List<bool>> instructions) {
    final reconstructedZones = <KnittingZone>[];

    for (final meta in metadata) {
      try {
        // Validate boundaries
        if (meta.startRow < 0 ||
            meta.endRow >= instructions.length ||
            meta.startNeedle < 0 ||
            meta.endNeedle >=
                (instructions.isNotEmpty ? instructions[0].length : 0)) {
          debugPrint(
              '[KnittingInstructionsManager] Invalid zone boundaries for ${meta.name}');
          continue;
        }

        // Extract zone instructions from the full pattern
        final zoneInstructions = _extractZoneInstructions(instructions, meta);

        if (zoneInstructions.isNotEmpty) {
          final zone = KnittingZone(
            name: meta.name,
            instructions: zoneInstructions,
            config: meta.config,
            startNeedle: meta.startNeedle,
            endNeedle: meta.endNeedle,
            startRow: meta.startRow,
            endRow: meta.endRow,
          );
          reconstructedZones.add(zone);
        } else {
          debugPrint(
              '[KnittingInstructionsManager] No valid instructions found for zone ${meta.name}');
        }
      } catch (e) {
        debugPrint(
            '[KnittingInstructionsManager] Error reconstructing zone ${meta.name}: $e');
      }
    }

    return reconstructedZones;
  }

  /// Extract zone instructions from the full pattern based on metadata
  List<List<bool>> _extractZoneInstructions(
      List<List<bool>> fullInstructions, ZoneMetadata metadata) {
    final zoneInstructions = <List<bool>>[];

    for (int row = metadata.startRow; row <= metadata.endRow; row++) {
      if (row >= 0 && row < fullInstructions.length) {
        final fullRow = fullInstructions[row];
        final zoneRow = <bool>[];

        for (int col = metadata.startNeedle; col <= metadata.endNeedle; col++) {
          if (col >= 0 && col < fullRow.length) {
            zoneRow.add(fullRow[col]);
          } else {
            zoneRow.add(false); // Pad with false if out of bounds
          }
        }

        zoneInstructions.add(zoneRow);
      } else {
        // Create empty row if out of bounds
        final emptyRow = List<bool>.filled(
            metadata.endNeedle - metadata.startNeedle + 1, false);
        zoneInstructions.add(emptyRow);
      }
    }

    return zoneInstructions;
  }

  /// Validate that zones are compatible with the current instructions
  bool _validateZonesAgainstInstructions(
      List<KnittingZone> zones, List<List<bool>> instructions) {
    if (zones.isEmpty || instructions.isEmpty) return zones.isEmpty;

    for (final zone in zones) {
      // Check bounds
      if (zone.startRow < 0 ||
          zone.endRow >= instructions.length ||
          zone.startNeedle < 0 ||
          zone.endNeedle >= instructions[0].length) {
        return false;
      }

      // Check that zone instructions match the global instructions in the zone's area
      for (int localRow = 0; localRow < zone.instructions.length; localRow++) {
        final globalRow = zone.startRow + localRow;
        if (globalRow >= instructions.length) return false;

        for (int localCol = 0;
            localCol < zone.instructions[localRow].length;
            localCol++) {
          final globalCol = zone.startNeedle + localCol;
          if (globalCol >= instructions[globalRow].length) return false;

          // If zone has a stitch, the global pattern should also have a stitch there
          if (zone.instructions[localRow][localCol] &&
              !instructions[globalRow][globalCol]) {
            return false;
          }
        }
      }
    }

    return true;
  }

  /// Save zones to wizard state with instruction hash (dual storage approach)
  Future<void> saveZonesToWizardState() async {
    try {
      if (!Get.isRegistered<NewItemWizardController>()) {
        return;
      }

      final wizardController = Get.find<NewItemWizardController>();
      if (wizardController.currentStateId.value == null) {
        return;
      }

      final state = await wizardController.wizardStateService
          .loadWizardState(wizardController.currentStateId.value!);

      if (state == null) {
        return;
      }

      // Calculate current instructions hash
      final instructionsHash =
          _calculateInstructionsHash(currentInstructions.value);

      // Create lightweight metadata for Firestore
      final zoneMetadata = knittingZones.value
          .map((zone) => ZoneMetadata.fromZone(zone))
          .toList();

      // Save with dual approach:
      // - Full zones for local storage (faster access)
      // - Lightweight metadata for Firestore (avoids size limits)
      final updatedState = state.copyWith(
        savedKnittingZones:
            List<KnittingZone>.from(knittingZones.value), // Local storage
        savedZoneMetadata: zoneMetadata, // Firestore storage
        instructionsHash: instructionsHash,
        lastModified: DateTime.now(),
      );

      await wizardController.wizardStateService.saveWizardState(updatedState);
      debugPrint(
          '[KnittingInstructionsManager] Saved ${knittingZones.value.length} zones with dual storage approach. Hash: $instructionsHash');
    } catch (e) {
      debugPrint('[KnittingInstructionsManager] Error saving zones: $e');
    }
  }

  /// Generate knitting instructions from the current shapes
  Future<List<List<bool>>> generateInstructions() async {
    isGeneratingInstructions.value = true;

    try {
      // Get all shape data from the controller's shapes
      final List<ShapeData> shapes = [];

      // Collect shape data from all shapes using the controller's getShapeState method
      for (final shape in _controller.shapes) {
        if (shape.key != null) {
          final shapeData = _controller.getShapeState(shape.key);
          if (shapeData != null) {
            shapes.add(shapeData);
          }
        }
      }

      final NewItemWizardController newItemWizardController =
          NewItemWizardController.to;

      final needleCount =
          newItemWizardController.newItem.value.knittingMachine!.needlesCount;
      final stitchesPerCm = newItemWizardController.newItem.value.stitchesPerCm;
      final rowsPerCm = newItemWizardController.newItem.value.rowsPerCm;

      // Calculate aspect ratio (stitch height / stitch width)
      double aspectRatio = 1.5; // Default value
      if (rowsPerCm != null &&
          rowsPerCm > 0 &&
          stitchesPerCm != null &&
          stitchesPerCm > 0) {
        aspectRatio = stitchesPerCm / rowsPerCm;
      }

      // Get grid dimensions from the grid system
      final gridSystem = _controller.gridSystem;
      final gaugeRatio = rowsPerCm! / stitchesPerCm!;

      // Set a large number of initial rows - at least 2x the calculated height
      // This gives plenty of vertical space for pattern design
      final numberOfRows = (needleCount * gaugeRatio * 2).round();
      final numberOfColumns =
          gridSystem.needleCount ?? 100; // Use needle count or default
      final cellWidth = gridSystem.cellWidth;

      // Validate gauge settings
      if (stitchesPerCm <= 0 || rowsPerCm <= 0) {
        Get.snackbar(
          'Warning',
          'Please set your knitting gauge in the settings first',
          backgroundColor: Colors.orange,
        );
        return [];
      }

      // Force update of needle mapping to ensure accurate calculations
      gridSystem.updateViewport(gridSystem.zoomLevel, gridSystem.panOffset,
          updateNeedleMapping: true);

      // Calculate combined bounds of all shapes
      Rect? combinedBounds;
      for (final shape in shapes) {
        // Get shape bounds
        final bounds = _getShapeBounds(shape);

        if (combinedBounds == null) {
          combinedBounds = bounds;
        } else {
          // Expand to include this shape
          combinedBounds = Rect.fromLTRB(
            math.min(combinedBounds.left, bounds.left),
            math.min(combinedBounds.top, bounds.top),
            math.max(combinedBounds.right, bounds.right),
            math.max(combinedBounds.bottom, bounds.bottom),
          );
        }
      }

      // Generate the stitch pattern using the converter
      // Ensure a valid aspect ratio
      final validAspectRatio =
          (aspectRatio <= 0 || aspectRatio.isNaN || aspectRatio.isInfinite)
              ? 1.5
              : aspectRatio;

      // --- Calculate cellWidth based on screen width and needle count ---
      final screenWidth = Get.width;
      final cellWidthForGeneration = (needleCount > 0 && screenWidth > 0)
          ? screenWidth / needleCount
          : 2.0; // Default fallback if needleCount or screenWidth is invalid
      debugPrint(
          'Using screenWidth / needleCount for generation cellWidth: $cellWidthForGeneration (Screen: $screenWidth, Needles: $needleCount)');
      // -----------------------------------------------------------------

      // Generate the instructions
      final newInstructions = convertShapesToStitchPattern(
        shapes: shapes,
        columns: needleCount,
        cellWidth: cellWidthForGeneration,
        gridSystem: gridSystem,
        boundingBox: combinedBounds,
        aspectRatio: validAspectRatio,
      );

      // Update current instructions
      currentInstructions.value = newInstructions;

      // Calculate pattern statistics
      patternStatistics.value =
          KnittingPatternGenerator.calculatePatternStatistics(newInstructions);

      if (newItemWizardController.currentKnittingRow.value == 10000) {
        newItemWizardController.currentKnittingRow.value =
            newInstructions.length - 1;
      }

      // Try to load saved zones first, only generate new ones if needed
      final loadedSavedZones =
          await _loadSavedZonesIfCompatible(newInstructions);

      if (!loadedSavedZones) {
        // Clear zone history before generating new zones
        _zoneHistoryManager.clearHistory();

        // Generate new zones and save them
        debugPrint('[KnittingInstructionsManager] Generating new zones');
        knittingZones.value = getKnittingZones();

        // Save the newly generated zones
        await saveZonesToWizardState();
      }

      return currentInstructions.value;
    } catch (e, stackTrace) {
      debugPrint('Error generating knitting instructions: $e');
      debugPrint('Stack trace: $stackTrace');
      return [];
    } finally {
      isGeneratingInstructions.value = false;
    }
  }

  /// Get the bounding rectangle for a shape, considering curves and rotation.
  Rect _getShapeBounds(ShapeData shape) {
    if (shape.vertices.isEmpty) {
      return Rect.zero;
    }

    // Use the path-based approach to get accurate bounds including curves
    final path = _createShapePath(shape);
    return path.getBounds();
  }

  /// Get a text representation of the current knitting pattern
  String getTextPattern() {
    return KnittingPatternGenerator.generateTextPattern(
      instructions: currentInstructions.value,
      startFromBottom: false, // Always use top-down
      includeRowNumbers: includeRowNumbers.value,
    );
  }

  /// Get a structured representation of the current knitting pattern
  List<List<String>> getStructuredPattern() {
    return KnittingPatternGenerator.generateStructuredPattern(
      instructions: currentInstructions.value,
      startFromBottom: false, // Always use top-down
    );
  }

  /// Toggle whether to include row numbers in the pattern text
  void toggleRowNumbers() {
    includeRowNumbers.value = !includeRowNumbers.value;
  }

  // -------------------------------------------------------------------
  // Helper functions adapted from knitting_instructions_generator_new.dart
  // -------------------------------------------------------------------

  /// Create a path for a single shape, properly handling rotation and curves
  ui.Path _createShapePath(ShapeData shape) {
    // Handle grouped shapes by recursively creating paths for each child shape
    // Note: This requires GroupShapeData definition or a dynamic check
    // Assuming ShapeData has a 'type' and potentially 'childShapes' for groups
    // if (shape.type == ShapeType.group && shape is GroupShapeData) { // Example if GroupShapeData is available
    // Using dynamic check as GroupShapeData might not be directly imported here
    if (shape.runtimeType.toString().contains('GroupShapeData')) {
      try {
        final dynamic groupShape = shape;
        if (groupShape.childShapes != null) {
          final childShapes = groupShape.childShapes as List<ShapeData>;

          if (childShapes.isEmpty) return ui.Path();

          // Create a path for each child shape and combine them
          ui.Path combinedPath = ui.Path();
          for (final childShape in childShapes) {
            final childPath = _createShapePath(childShape);
            combinedPath.addPath(childPath, Offset.zero);
          }
          return combinedPath;
        }
      } catch (e) {
        debugPrint("Error processing group shape in _createShapePath: $e");
        // Fallback to using group's own vertices if child access fails
      }
    }

    final path = ui.Path();

    // Skip shapes with no vertices
    if (shape.vertices.isEmpty) return path;

    // Get all vertices, applying rotation if needed
    final vertices = shape.rotation != 0
        ? shape.vertices
            .map((v) => _rotatePoint(v, shape.center, shape.rotation))
            .toList()
        : shape.vertices;

    if (vertices.isEmpty) return path; // Check again after potential rotation

    // Move to the first vertex
    path.moveTo(vertices[0].dx, vertices[0].dy);

    // Draw each edge of the shape
    for (int i = 0; i < vertices.length; i++) {
      final nextIndex = (i + 1) % vertices.length;
      final currentVertex = vertices[i];
      final nextVertex = vertices[nextIndex];

      // Check if this edge has a curve control point
      if (shape.curveControls.containsKey(i)) {
        // Calculate the midpoint of the *rotated* edge
        final midpoint = Offset((currentVertex.dx + nextVertex.dx) / 2,
            (currentVertex.dy + nextVertex.dy) / 2);

        // Get the control point offset (relative to the midpoint)
        final controlOffset = shape.curveControls[i]!;

        // Calculate the absolute control point position
        final controlPoint =
            midpoint.translate(controlOffset.dx, controlOffset.dy);

        // NOTE: Unlike the version in TransformableShape, the vertices here are ALREADY rotated.
        // The control point calculated from the rotated midpoint also needs to be considered in the rotated space.
        // No further rotation needed for the control point itself IF vertices were pre-rotated.
        final effectiveControlPoint = controlPoint;

        // Add a quadratic bezier curve to the path
        path.quadraticBezierTo(effectiveControlPoint.dx,
            effectiveControlPoint.dy, nextVertex.dx, nextVertex.dy);
      } else {
        // Add a straight line to the next vertex
        path.lineTo(nextVertex.dx, nextVertex.dy);
      }
    }

    // Close the path
    path.close();
    return path;
  }

  /// Helper function to rotate a point around a center
  Offset _rotatePoint(Offset point, Offset center, double angle) {
    // Calculate position relative to center
    final dx = point.dx - center.dx;
    final dy = point.dy - center.dy;

    // Apply rotation
    final cosTheta = math.cos(angle);
    final sinTheta = math.sin(angle);

    final rotatedDx = dx * cosTheta - dy * sinTheta;
    final rotatedDy = dx * sinTheta + dy * cosTheta;

    // Return point in original coordinate system
    return Offset(center.dx + rotatedDx, center.dy + rotatedDy);
  }

  /// Helper to find all bottom-most starting positions on the current lowest active row.
  /// The returned list is sorted by column index according to startFromRight.
  List<Map<String, int>> _findAllBottomMostStartingPoints(
      List<List<bool>> instructions, bool startFromRight) {
    if (instructions.isEmpty) return [];

    int minRowWithStitches = -1;
    for (int r = instructions.length - 1; r >= 0; r--) {
      if (instructions[r].contains(true)) {
        minRowWithStitches = r;
        break;
      }
    }

    if (minRowWithStitches == -1) return []; // No stitches found

    final List<Map<String, int>> starts = [];
    final rowData = instructions[minRowWithStitches];

    if (startFromRight) {
      for (int col = rowData.length - 1; col >= 0; col--) {
        if (rowData[col]) {
          starts.add({'row': minRowWithStitches, 'col': col});
        }
      }
    } else {
      for (int col = 0; col < rowData.length; col++) {
        if (rowData[col]) {
          starts.add({'row': minRowWithStitches, 'col': col});
        }
      }
    }
    return starts;
  }

  /// Get the knitting zones from the current instructions using an improved algorithm
  /// that handles contiguous and discontinuous rows appropriately
  List<KnittingZone> getKnittingZones({bool startFromRight = true}) {
    final List<List<bool>> workingInstructions = List.generate(
        currentInstructions.value.length,
        (r) => List.from(currentInstructions.value[r]));

    List<KnittingZone> knittingZones = [];
    int zoneCount = 1;

    // Process the pattern from bottom to top using the new algorithm
    _processPatternByContiguity(workingInstructions, knittingZones, zoneCount);

    // Sort zones from bottom right to top left and renumber them
    _sortAndRenumberZones(knittingZones);

    return knittingZones;
  }

  /// Process the pattern by analyzing contiguity patterns from bottom to top
  void _processPatternByContiguity(
    List<List<bool>> workingInstructions,
    List<KnittingZone> knittingZones,
    int zoneCount,
  ) {
    if (!_hasStitches(workingInstructions)) return;

    // Find the bottommost row with stitches
    int bottomRowIndex = -1;
    for (int row = workingInstructions.length - 1; row >= 0; row--) {
      if (workingInstructions[row].contains(true)) {
        bottomRowIndex = row;
        break;
      }
    }

    if (bottomRowIndex == -1) return; // No stitches found

    // Determine if the bottom row is contiguous
    bool isBottomRowContiguous =
        _isRowContiguous(workingInstructions[bottomRowIndex]);

    // Scan upward until we find a row with different contiguity pattern or no stitches
    int endOfBlock = bottomRowIndex;
    int startOfBlock = bottomRowIndex;

    for (int row = bottomRowIndex - 1; row >= 0; row--) {
      final rowData = workingInstructions[row];

      if (!rowData.contains(true)) {
        // No stitches in this row, marks the end of the current block
        endOfBlock = row + 1;
        break;
      }

      bool isCurrentRowContiguous = _isRowContiguous(rowData);

      if (isCurrentRowContiguous != isBottomRowContiguous) {
        // Contiguity pattern changed, marks the end of the current block
        endOfBlock = row + 1;
        break;
      }

      startOfBlock = row;
    }

    // If we reached the top without a change in contiguity pattern, set endOfBlock to top
    if (endOfBlock == bottomRowIndex) {
      endOfBlock = startOfBlock;
    }

    // Extract the block from startOfBlock to bottomRowIndex
    List<List<bool>> blockInstructions =
        _extractBlock(workingInstructions, startOfBlock, bottomRowIndex);

    // Process this block based on its contiguity
    if (isBottomRowContiguous) {
      // For contiguous blocks, create a single zone
      _createZoneFromBlock(blockInstructions, knittingZones, zoneCount,
          startOfBlock, bottomRowIndex);
      zoneCount++;
    } else {
      // For discontinuous blocks, split into multiple zones by columns
      _splitDiscontinuousBlockIntoZones(blockInstructions, knittingZones,
          zoneCount, startOfBlock, bottomRowIndex);
      zoneCount += _countDistinctColumnGroups(blockInstructions);
    }

    // Recursively process the rest of the pattern (if any)
    if (startOfBlock > 0) {
      _processPatternByContiguity(
          workingInstructions, knittingZones, zoneCount);
    }
  }

  // Helper function to generate letter-based zone names (A, B, C, ..., Z, AA, AB, etc.)
  String _generateZoneName(int index) {
    String result = '';
    int columnNumber = index + 1; // Convert 0-based to 1-based

    while (columnNumber > 0) {
      columnNumber--; // Make it 0-based for this iteration
      result = String.fromCharCode(65 + (columnNumber % 26)) + result;
      columnNumber = columnNumber ~/ 26;
    }

    return 'Zone $result';
  }

  // Helper function to check if a name matches the default pattern (Zone A, Zone B, etc.)
  bool isDefaultZoneName(String name) {
    final pattern = RegExp(r'^Zone [A-Z]+$');
    return pattern.hasMatch(name);
  }

  /// Sort zones from bottom right to top left and renumber them starting from A
  void _sortAndRenumberZones(List<KnittingZone> zones) {
    if (zones.isEmpty) return;

    // Sort from bottom to top (higher endRow to lower endRow)
    // For same endRow, sort from right to left (higher endNeedle to lower endNeedle)
    zones.sort((a, b) {
      // First compare by bottom row (endRow) - sort from bottom to top
      int endRowComparison = b.endRow.compareTo(a.endRow);
      if (endRowComparison != 0) {
        return endRowComparison;
      }

      // If they have the same bottom row, compare by right edge (endNeedle) - sort from right to left
      return b.endNeedle.compareTo(a.endNeedle);
    });

    // Rename zones using letters (only for default names, preserve custom names)
    for (int i = 0; i < zones.length; i++) {
      // Only rename zones that have default names, preserve custom names
      if (isDefaultZoneName(zones[i].name)) {
        zones[i] = zones[i].copyWith(name: _generateZoneName(i));
      }
    }
  }

  /// Check if a row is contiguous (all true values form a single block with no gaps)
  bool _isRowContiguous(List<bool> row) {
    int firstTrue = -1;
    int lastTrue = -1;

    for (int i = 0; i < row.length; i++) {
      if (row[i]) {
        if (firstTrue == -1) firstTrue = i;
        lastTrue = i;
      }
    }

    // If no true values or only one true value, it's contiguous
    if (firstTrue == -1 || firstTrue == lastTrue) return true;

    // Check if there are any false values between the first and last true values
    for (int i = firstTrue; i <= lastTrue; i++) {
      if (!row[i]) return false;
    }

    return true;
  }

  /// Extract a block of instructions from startRow to endRow
  List<List<bool>> _extractBlock(
      List<List<bool>> instructions, int startRow, int endRow) {
    final List<List<bool>> block = [];

    for (int row = startRow; row <= endRow; row++) {
      // Create a copy of the row
      final rowData = List<bool>.from(instructions[row]);
      block.add(rowData);

      // Clear the row in the original instructions
      for (int col = 0; col < instructions[row].length; col++) {
        if (instructions[row][col]) {
          instructions[row][col] = false;
        }
      }
    }

    return block;
  }

  /// Create a zone from a contiguous block
  void _createZoneFromBlock(
      List<List<bool>> blockInstructions,
      List<KnittingZone> knittingZones,
      int zoneCount,
      int globalStartRow,
      int globalEndRow) {
    if (blockInstructions.isEmpty) return;

    // Minimum thresholds for creating a zone
    const int MIN_ROWS = 3; // Minimum number of rows required
    const int MIN_STITCHES = 5; // Minimum total stitches required

    // Check if we have enough rows
    if (globalEndRow - globalStartRow + 1 < MIN_ROWS) {
      // Skip tiny zones with just 1-2 rows
      return;
    }

    // Find the min and max columns of the content within the block (these are global indices)
    int globalMinCol =
        blockInstructions.isNotEmpty ? blockInstructions[0].length : 0;
    int globalMaxCol = 0;
    bool hasContent = false;
    int totalStitches = 0;

    for (final row in blockInstructions) {
      for (int col = 0; col < row.length; col++) {
        if (row[col]) {
          totalStitches++;
          hasContent = true;
          globalMinCol = math.min(globalMinCol, col);
          globalMaxCol = math.max(globalMaxCol, col);
        }
      }
    }

    // Check if this zone has the minimum required stitches
    if (!hasContent || totalStitches < MIN_STITCHES) return;

    // Create CROPPED instructions for the zone based on globalMinCol and globalMaxCol
    List<List<bool>> croppedZoneInstructions = [];
    if (globalMinCol <= globalMaxCol) {
      // Ensure valid column range
      for (final fullWidthRow in blockInstructions) {
        // Sublist from globalMinCol to globalMaxCol+1 from the fullWidthRow
        // Ensure sublist range is valid for each row, though it should be if hasContent is true
        // and globalMinCol/globalMaxCol were derived from these rows.
        croppedZoneInstructions
            .add(fullWidthRow.sublist(globalMinCol, globalMaxCol + 1));
      }
    } else {
      // This case should ideally not be reached if hasContent is true.
      // If it is, it implies an issue with min/max col calculation or empty block.
      // For safety, if croppedZoneInstructions would be empty or invalid, return.
      return;
    }

    // Ensure croppedZoneInstructions is not empty if we proceed
    if (croppedZoneInstructions.isEmpty ||
        croppedZoneInstructions.every((row) => row.isEmpty)) {
      return; // Don't create a zone with no instruction data
    }

    final zone = KnittingZone(
      name: _generateZoneName(zoneCount - 1), // Convert to 0-based index
      instructions: croppedZoneInstructions, // Pass CROPPED instructions
      startNeedle: globalMinCol, // Global start needle for this zone
      endNeedle: globalMaxCol, // Global end needle for this zone
      startRow: globalStartRow, // Global start row for this zone
      endRow: globalEndRow, // Global end row for this zone
    );

    knittingZones.add(zone);
  }

  /// Split a discontinuous block into multiple zones by column grouping
  void _splitDiscontinuousBlockIntoZones(
      List<List<bool>> blockInstructions,
      List<KnittingZone> knittingZones,
      int zoneCount,
      int startRow,
      int endRow) {
    if (blockInstructions.isEmpty) return;

    // Minimum thresholds for creating a zone
    const int MIN_ROWS = 3; // Minimum number of rows required
    const int MIN_STITCHES = 5; // Minimum total stitches required

    // Check if we have enough rows
    if (endRow - startRow + 1 < MIN_ROWS) {
      // Skip tiny zones with just 1-2 rows
      return;
    }

    // Identify distinct column groups in the block
    final List<Map<String, int>> columnGroups =
        _identifyColumnGroups(blockInstructions);

    // Create a zone for each column group
    for (int i = 0; i < columnGroups.length; i++) {
      final group = columnGroups[i];
      final int minCol = group['start']!;
      final int maxCol = group['end']!;

      // Extract this column group as a separate block
      final List<List<bool>> zoneInstructions = [];
      int totalStitches = 0;

      for (final row in blockInstructions) {
        final List<bool> zoneRow = List.filled(maxCol - minCol + 1, false);

        for (int col = minCol; col <= maxCol; col++) {
          if (col < row.length && row[col]) {
            zoneRow[col - minCol] = true;
            totalStitches++;
          }
        }

        zoneInstructions.add(zoneRow);
      }

      // Check if this zone has the minimum required stitches
      if (totalStitches < MIN_STITCHES) continue;

      // Create a zone if it has any stitches
      if (_hasStitches(zoneInstructions)) {
        final zone = KnittingZone(
          name:
              _generateZoneName(zoneCount + i - 1), // Convert to 0-based index
          instructions: zoneInstructions,
          startNeedle: minCol,
          endNeedle: maxCol,
          startRow: startRow,
          endRow: endRow,
        );

        knittingZones.add(zone);
      }
    }
  }

  /// Identify distinct column groups in a discontinuous block
  List<Map<String, int>> _identifyColumnGroups(
      List<List<bool>> blockInstructions) {
    final List<Map<String, int>> groups = [];

    // Create a merged row representing all columns that have stitches in any row
    final List<bool> mergedRow =
        List.filled(blockInstructions[0].length, false);

    for (final row in blockInstructions) {
      for (int col = 0; col < row.length; col++) {
        if (row[col]) {
          mergedRow[col] = true;
        }
      }
    }

    // Identify continuous groups in the merged row
    int startCol = -1;

    for (int col = 0; col < mergedRow.length; col++) {
      if (mergedRow[col]) {
        if (startCol == -1) {
          startCol = col;
        }
      } else {
        if (startCol != -1) {
          groups.add({'start': startCol, 'end': col - 1});
          startCol = -1;
        }
      }
    }

    // Don't forget the last group if it extends to the end
    if (startCol != -1) {
      groups.add({'start': startCol, 'end': mergedRow.length - 1});
    }

    return groups;
  }

  /// Count the number of distinct column groups in a block
  int _countDistinctColumnGroups(List<List<bool>> blockInstructions) {
    return _identifyColumnGroups(blockInstructions).length;
  }

  /// Helper method to extract a portion of the instructions into a zone
  KnittingZone _extractZoneFromInstructions(List<List<bool>> instructions,
      int startRow, int endRow, String zoneName) {
    if (startRow > endRow || instructions.isEmpty) {
      return KnittingZone(name: zoneName, instructions: []);
    }

    // Calculate the min and max columns
    int minCol = instructions[0].length;
    int maxCol = 0;

    for (int row = startRow; row <= endRow; row++) {
      if (row >= instructions.length) continue;

      for (int col = 0; col < instructions[row].length; col++) {
        if (instructions[row][col]) {
          minCol = math.min(minCol, col);
          maxCol = math.max(maxCol, col);
        }
      }
    }

    // Handle case where no stitches were found
    if (minCol > maxCol) {
      return KnittingZone(name: zoneName, instructions: []);
    }

    // Extract the zone instructions
    final List<List<bool>> zoneInstructions = [];

    for (int row = startRow; row <= endRow; row++) {
      if (row >= instructions.length) continue;

      final rowData = instructions[row];
      final List<bool> zoneRow = List.filled(maxCol - minCol + 1, false);

      for (int col = minCol; col <= maxCol; col++) {
        if (col < rowData.length && rowData[col]) {
          zoneRow[col - minCol] = true;
        }
      }

      zoneInstructions.add(zoneRow);
    }

    return KnittingZone(
      name: zoneName,
      instructions: zoneInstructions,
      startNeedle: minCol,
      endNeedle: maxCol,
      startRow: startRow,
      endRow: endRow,
    );
  }

  // Helper method to check if there are any stitches left in the working instructions
  bool _hasStitches(List<List<bool>> instructions) {
    for (final row in instructions) {
      if (row.contains(true)) {
        return true;
      }
    }
    return false;
  }

  /// Get all zones with trimmed instructions for display purposes
  /// This removes padding spaces (false values) while maintaining position information
  List<Map<String, dynamic>> getTrimmedKnittingZones() {
    final zones = knittingZones.value;
    final result = <Map<String, dynamic>>[];

    for (int i = 0; i < zones.length; i++) {
      final zone = zones[i];
      final trimmed = zone.getTrimmedInstructions();

      result.add({
        'name': zone.name,
        'instructions': trimmed['instructions'],
        'config': zone.config.value.toJson(),
        // Original position data
        'startNeedle': zone.startNeedle,
        'endNeedle': zone.endNeedle,
        'startRow': zone.startRow,
        'endRow': zone.endRow,
        // Trim information
        'leftTrim': trimmed['leftTrim'],
        'rightTrim': trimmed['rightTrim'],
        'topTrim': trimmed['topTrim'],
        'bottomTrim': trimmed['bottomTrim'],
        // Calculate actual needle positions after trimming
        'displayStartNeedle': zone.startNeedle + trimmed['leftTrim'],
        'displayEndNeedle': zone.endNeedle - trimmed['rightTrim'],
        'displayStartRow': zone.startRow + trimmed['topTrim'],
        'displayEndRow': zone.endRow - trimmed['bottomTrim'],
      });
    }

    return result;
  }

// Helper method to check if position is valid
  bool _isValidPosition(int position, int length) {
    return position >= 0 && position < length;
  }
}

/// represents an area of the pattern which can be knitted continuously from bottom to top
/// it is a list of rows, each row is a list of stitches
class KnittingZone {
  final String name;
  final List<List<bool>> instructions;
  final Rx<KnittingZoneConfig> config;

  // Position information relative to the full pattern
  final int startNeedle; // Leftmost needle position in the full pattern
  final int endNeedle; // Rightmost needle position in the full pattern
  final int startRow; // Top row position in the full pattern
  final int endRow; // Bottom row position in the full pattern

  KnittingZone({
    required this.name,
    required this.instructions,
    KnittingZoneConfig? config,
    this.startNeedle = 0,
    this.endNeedle = 0,
    this.startRow = 0,
    this.endRow = 0,
  }) : config = Rx<KnittingZoneConfig>(config ?? KnittingZoneConfig());

  void appendRow(List<bool> row) {
    instructions.insert(0, row);
  }

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'instructions': instructions,
      'config': config.value.toJson(),
      'startNeedle': startNeedle,
      'endNeedle': endNeedle,
      'startRow': startRow,
      'endRow': endRow,
    };
  }

  factory KnittingZone.fromJson(Map<String, dynamic> json) {
    return KnittingZone(
      name: json['name'] ?? '',
      instructions: (json['instructions'] as List?)
              ?.map((row) => (row as List).cast<bool>())
              .toList() ??
          [],
      config: json['config'] != null
          ? KnittingZoneConfig.fromJson(json['config'])
          : null,
      startNeedle: json['startNeedle'] ?? 0,
      endNeedle: json['endNeedle'] ?? 0,
      startRow: json['startRow'] ?? 0,
      endRow: json['endRow'] ?? 0,
    );
  }

  /// Returns a trimmed version of instructions with padding spaces (false values) removed
  /// This is useful for display purposes while keeping original position data
  Map<String, dynamic> getTrimmedInstructions() {
    if (instructions.isEmpty) {
      return {
        'instructions': <List<bool>>[],
        'leftTrim': 0,
        'rightTrim': 0,
        'topTrim': 0,
        'bottomTrim': 0
      };
    }

    // Find the leftmost and rightmost columns that contain true values
    int leftBound = instructions[0].length;
    int rightBound = 0;

    // Also find top and bottom rows with content
    int topBound = -1;
    int bottomBound = -1;

    // Find the boundaries of the actual content
    for (int rowIndex = 0; rowIndex < instructions.length; rowIndex++) {
      final row = instructions[rowIndex];
      bool rowHasStitches = false;

      for (int colIndex = 0; colIndex < row.length; colIndex++) {
        if (row[colIndex]) {
          rowHasStitches = true;
          leftBound = math.min(leftBound, colIndex);
          rightBound = math.max(rightBound, colIndex);
        }
      }

      if (rowHasStitches) {
        if (topBound == -1) topBound = rowIndex;
        bottomBound = rowIndex;
      }
    }

    // Guard against empty zones
    if (leftBound > rightBound || topBound == -1) {
      return {
        'instructions': <List<bool>>[],
        'leftTrim': 0,
        'rightTrim': 0,
        'topTrim': 0,
        'bottomTrim': 0
      };
    }

    // Extract the trimmed instructions
    final trimmedInstructions = <List<bool>>[];
    for (int rowIndex = topBound; rowIndex <= bottomBound; rowIndex++) {
      final row = instructions[rowIndex];
      final trimmedRow = row.sublist(leftBound, rightBound + 1);
      trimmedInstructions.add(trimmedRow);
    }

    return {
      'instructions': trimmedInstructions,
      'leftTrim': leftBound,
      'rightTrim': instructions[0].length - rightBound - 1,
      'topTrim': topBound,
      'bottomTrim': instructions.length - bottomBound - 1
    };
  }

  KnittingZone copyWith({
    String? name,
    List<List<bool>>? instructions,
    KnittingZoneConfig? config,
    int? startNeedle,
    int? endNeedle,
    int? startRow,
    int? endRow,
  }) {
    return KnittingZone(
      name: name ?? this.name,
      instructions: instructions ?? this.instructions,
      config: config ?? this.config.value,
      startNeedle: startNeedle ?? this.startNeedle,
      endNeedle: endNeedle ?? this.endNeedle,
      startRow: startRow ?? this.startRow,
      endRow: endRow ?? this.endRow,
    );
  }
}

/// Configuration for a knitting zone
class KnittingZoneConfig {
  final RxBool autoControlCarriagePosition;
  final RxBool autoAdjustAsymmetricalRows;
  final RxBool autoControlIncreaseDecrease;
  final RxString carriagePosition; // "left" or "right"
  final RxBool isAsymmetrical; // yes or no
  final RxString asymmetricalDirection; // "inc" or "dec"
  final RxInt increaseBy;
  final RxInt increaseEvery;
  final RxInt decreaseBy;
  final RxInt decreaseEvery;
  final RxBool isEmpty; // If this is an empty zone
  final RxString finishingMethod; // "bind off" or "use scrap"

  KnittingZoneConfig({
    bool autoControlCarriagePosition = true,
    bool autoAdjustAsymmetricalRows = true,
    bool autoControlIncreaseDecrease = true,
    String carriagePosition = "left",
    bool isAsymmetrical = false,
    String asymmetricalDirection = "inc",
    int increaseBy = 0,
    int increaseEvery = 0,
    int decreaseBy = 0,
    int decreaseEvery = 0,
    bool isEmpty = false,
    String finishingMethod = "bind off",
  })  : autoControlCarriagePosition = autoControlCarriagePosition.obs,
        autoAdjustAsymmetricalRows = autoAdjustAsymmetricalRows.obs,
        autoControlIncreaseDecrease = autoControlIncreaseDecrease.obs,
        carriagePosition = carriagePosition.obs,
        isAsymmetrical = isAsymmetrical.obs,
        asymmetricalDirection = asymmetricalDirection.obs,
        increaseBy = increaseBy.obs,
        increaseEvery = increaseEvery.obs,
        decreaseBy = decreaseBy.obs,
        decreaseEvery = decreaseEvery.obs,
        isEmpty = isEmpty.obs,
        finishingMethod = finishingMethod.obs;

  Map<String, dynamic> toJson() {
    return {
      'autoControlCarriagePosition': autoControlCarriagePosition.value,
      'autoAdjustAsymmetricalRows': autoAdjustAsymmetricalRows.value,
      'autoControlIncreaseDecrease': autoControlIncreaseDecrease.value,
      'carriagePosition': carriagePosition.value,
      'isAsymmetrical': isAsymmetrical.value,
      'asymmetricalDirection': asymmetricalDirection.value,
      'increaseBy': increaseBy.value,
      'increaseEvery': increaseEvery.value,
      'decreaseBy': decreaseBy.value,
      'decreaseEvery': decreaseEvery.value,
      'isEmpty': isEmpty.value,
      'finishingMethod': finishingMethod.value,
    };
  }

  factory KnittingZoneConfig.fromJson(Map<String, dynamic> json) {
    return KnittingZoneConfig(
      autoControlCarriagePosition: json['autoControlCarriagePosition'] ?? true,
      autoAdjustAsymmetricalRows: json['autoAdjustAsymmetricalRows'] ?? true,
      autoControlIncreaseDecrease: json['autoControlIncreaseDecrease'] ?? true,
      carriagePosition: json['carriagePosition'] ?? "left",
      isAsymmetrical: json['isAsymmetrical'] ?? false,
      asymmetricalDirection: json['asymmetricalDirection'] ?? "inc",
      increaseBy: json['increaseBy'] ?? 0,
      increaseEvery: json['increaseEvery'] ?? 0,
      decreaseBy: json['decreaseBy'] ?? 0,
      decreaseEvery: json['decreaseEvery'] ?? 0,
      isEmpty: json['isEmpty'] ?? false,
      finishingMethod: json['finishingMethod'] ?? "bind off",
    );
  }

  KnittingZoneConfig copyWith({
    bool? autoControlCarriagePosition,
    bool? autoAdjustAsymmetricalRows,
    bool? autoControlIncreaseDecrease,
    String? carriagePosition,
    bool? isAsymmetrical,
    String? asymmetricalDirection,
    int? increaseBy,
    int? increaseEvery,
    int? decreaseBy,
    int? decreaseEvery,
    bool? isEmpty,
    String? finishingMethod,
  }) {
    return KnittingZoneConfig(
      autoControlCarriagePosition:
          autoControlCarriagePosition ?? this.autoControlCarriagePosition.value,
      autoAdjustAsymmetricalRows:
          autoAdjustAsymmetricalRows ?? this.autoAdjustAsymmetricalRows.value,
      autoControlIncreaseDecrease:
          autoControlIncreaseDecrease ?? this.autoControlIncreaseDecrease.value,
      carriagePosition: carriagePosition ?? this.carriagePosition.value,
      isAsymmetrical: isAsymmetrical ?? this.isAsymmetrical.value,
      asymmetricalDirection:
          asymmetricalDirection ?? this.asymmetricalDirection.value,
      increaseBy: increaseBy ?? this.increaseBy.value,
      increaseEvery: increaseEvery ?? this.increaseEvery.value,
      decreaseBy: decreaseBy ?? this.decreaseBy.value,
      decreaseEvery: decreaseEvery ?? this.decreaseEvery.value,
      isEmpty: isEmpty ?? this.isEmpty.value,
      finishingMethod: finishingMethod ?? this.finishingMethod.value,
    );
  }
}

/// Lightweight zone metadata for Firestore storage (without large instruction arrays)
class ZoneMetadata {
  final String name;
  final int startNeedle;
  final int endNeedle;
  final int startRow;
  final int endRow;
  final KnittingZoneConfig config;

  ZoneMetadata({
    required this.name,
    required this.startNeedle,
    required this.endNeedle,
    required this.startRow,
    required this.endRow,
    required this.config,
  });

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'startNeedle': startNeedle,
      'endNeedle': endNeedle,
      'startRow': startRow,
      'endRow': endRow,
      'config': config.toJson(),
    };
  }

  factory ZoneMetadata.fromJson(Map<String, dynamic> json) {
    return ZoneMetadata(
      name: json['name'] ?? '',
      startNeedle: json['startNeedle'] ?? 0,
      endNeedle: json['endNeedle'] ?? 0,
      startRow: json['startRow'] ?? 0,
      endRow: json['endRow'] ?? 0,
      config: json['config'] != null
          ? KnittingZoneConfig.fromJson(json['config'])
          : KnittingZoneConfig(),
    );
  }

  /// Create metadata from a full KnittingZone
  factory ZoneMetadata.fromZone(KnittingZone zone) {
    return ZoneMetadata(
      name: zone.name,
      startNeedle: zone.startNeedle,
      endNeedle: zone.endNeedle,
      startRow: zone.startRow,
      endRow: zone.endRow,
      config: zone.config.value,
    );
  }
}

/// Zone history entry for undo/redo functionality
class ZoneHistoryEntry {
  final List<KnittingZone> zones;
  final int? selectedZoneIndex;
  final String? operationName;
  final DateTime timestamp;

  ZoneHistoryEntry({
    required this.zones,
    this.selectedZoneIndex,
    this.operationName,
    DateTime? timestamp,
  }) : timestamp = timestamp ?? DateTime.now();

  ZoneHistoryEntry copyWith({
    List<KnittingZone>? zones,
    int? selectedZoneIndex,
    String? operationName,
    DateTime? timestamp,
  }) {
    return ZoneHistoryEntry(
      zones: zones ?? this.zones.map((z) => z.copyWith()).toList(),
      selectedZoneIndex: selectedZoneIndex ?? this.selectedZoneIndex,
      operationName: operationName ?? this.operationName,
      timestamp: timestamp ?? this.timestamp,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'zones': zones.map((z) => z.toJson()).toList(),
      'selectedZoneIndex': selectedZoneIndex,
      'operationName': operationName,
      'timestamp': timestamp.toIso8601String(),
    };
  }

  factory ZoneHistoryEntry.fromJson(Map<String, dynamic> json) {
    return ZoneHistoryEntry(
      zones: (json['zones'] as List?)
              ?.map((z) => KnittingZone.fromJson(z))
              .toList() ??
          [],
      selectedZoneIndex: json['selectedZoneIndex'],
      operationName: json['operationName'],
      timestamp: DateTime.parse(json['timestamp']),
    );
  }
}

/// Zone history manager for undo/redo functionality
///
/// This class provides complete history tracking for all zone operations including:
/// - Creating new zones
/// - Deleting zones
/// - Resizing zone boundaries
/// - Renaming zones
/// - Reordering zones
///
/// Features:
/// - Smart tracking that only records actual changes
/// - Configurable history size limit (default: 50 operations)
/// - Reactive observables for UI integration
/// - Detailed operation names for user feedback
/// - Start/finish tracking pattern for complex operations
/// - Cancel tracking for failed operations
///
/// Usage:
/// ```dart
/// // For simple operations
/// historyManager.addHistoryEntry(zones, selectedIndex, "Operation Name");
///
/// // For complex operations
/// historyManager.startTracking(zones, selectedIndex, "Operation Name");
/// // ... perform modifications ...
/// historyManager.finishTracking(newZones, newSelectedIndex);
///
/// // For failed operations
/// historyManager.cancelTracking();
/// ```
class ZoneHistoryManager {
  final List<ZoneHistoryEntry> _history = [];
  final List<ZoneHistoryEntry> _redoStack = [];
  int _maxHistorySize = 50;

  // Observables for UI
  final RxBool canUndo = false.obs;
  final RxBool canRedo = false.obs;
  final Rx<String?> lastOperation = Rx<String?>(null);

  // Track if we're currently in the middle of an operation
  ZoneHistoryEntry? _trackingEntry;

  /// Start tracking a zone operation
  void startTracking(List<KnittingZone> currentZones, int? selectedIndex,
      [String? operationName]) {
    _trackingEntry = ZoneHistoryEntry(
      zones: currentZones.map((z) => z.copyWith()).toList(), // Deep copy
      selectedZoneIndex: selectedIndex,
      operationName: operationName,
    );
  }

  /// Finish tracking and add to history if changes occurred
  void finishTracking(List<KnittingZone> newZones, int? newSelectedIndex) {
    if (_trackingEntry == null) return;

    // Check if zones actually changed
    bool hasChanges = _hasZoneChanges(_trackingEntry!.zones, newZones) ||
        _trackingEntry!.selectedZoneIndex != newSelectedIndex;

    if (hasChanges) {
      _addHistoryEntry(_trackingEntry!);
    }

    _trackingEntry = null;
  }

  /// Cancel current tracking without adding to history
  void cancelTracking() {
    _trackingEntry = null;
  }

  /// Add entry directly to history (for operations that don't need tracking)
  void addHistoryEntry(List<KnittingZone> zones, int? selectedIndex,
      [String? operationName]) {
    final entry = ZoneHistoryEntry(
      zones: zones.map((z) => z.copyWith()).toList(),
      selectedZoneIndex: selectedIndex,
      operationName: operationName,
    );
    _addHistoryEntry(entry);
  }

  void _addHistoryEntry(ZoneHistoryEntry entry) {
    // Clear redo stack when new entry is added
    _redoStack.clear();

    // Add to history
    _history.add(entry);

    // Maintain size limit
    if (_history.length > _maxHistorySize) {
      _history.removeAt(0);
    }

    _updateObservables();
    debugPrint(
        '[ZoneHistoryManager] Added history entry: ${entry.operationName}');
  }

  /// Undo last operation
  ZoneHistoryEntry? undo() {
    if (_history.isEmpty) return null;

    // Move current state to redo stack first
    final currentEntry = _history.removeLast();
    _redoStack.add(currentEntry);

    // Get previous state
    ZoneHistoryEntry? previousEntry;
    if (_history.isNotEmpty) {
      previousEntry = _history.last;
    }

    _updateObservables();
    debugPrint('[ZoneHistoryManager] Undoing: ${currentEntry.operationName}');

    return previousEntry;
  }

  /// Redo previously undone operation
  ZoneHistoryEntry? redo() {
    if (_redoStack.isEmpty) return null;

    final redoEntry = _redoStack.removeLast();
    _history.add(redoEntry);

    _updateObservables();
    debugPrint('[ZoneHistoryManager] Redoing: ${redoEntry.operationName}');

    return redoEntry;
  }

  /// Get the name of the last operation that can be undone
  String? getLastUndoOperationName() {
    return _history.isNotEmpty ? _history.last.operationName : null;
  }

  /// Get the name of the next operation that can be redone
  String? getNextRedoOperationName() {
    return _redoStack.isNotEmpty ? _redoStack.last.operationName : null;
  }

  /// Clear all history
  void clearHistory() {
    _history.clear();
    _redoStack.clear();
    _trackingEntry = null;
    _updateObservables();
    debugPrint('[ZoneHistoryManager] History cleared');
  }

  /// Check if zones have changed
  bool _hasZoneChanges(
      List<KnittingZone> oldZones, List<KnittingZone> newZones) {
    if (oldZones.length != newZones.length) return true;

    for (int i = 0; i < oldZones.length; i++) {
      final oldZone = oldZones[i];
      final newZone = newZones[i];

      // Check basic properties
      if (oldZone.name != newZone.name ||
          oldZone.startNeedle != newZone.startNeedle ||
          oldZone.endNeedle != newZone.endNeedle ||
          oldZone.startRow != newZone.startRow ||
          oldZone.endRow != newZone.endRow) {
        return true;
      }

      // Check instructions
      if (oldZone.instructions.length != newZone.instructions.length) {
        return true;
      }

      for (int r = 0; r < oldZone.instructions.length; r++) {
        if (oldZone.instructions[r].length != newZone.instructions[r].length) {
          return true;
        }
        for (int c = 0; c < oldZone.instructions[r].length; c++) {
          if (oldZone.instructions[r][c] != newZone.instructions[r][c]) {
            return true;
          }
        }
      }
    }

    return false;
  }

  void _updateObservables() {
    canUndo.value = _history.isNotEmpty;
    canRedo.value = _redoStack.isNotEmpty;
    lastOperation.value = getLastUndoOperationName();
  }

  /// Get history size for debugging
  int get historySize => _history.length;
  int get redoStackSize => _redoStack.length;
}
