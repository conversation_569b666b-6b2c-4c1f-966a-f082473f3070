import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'dart:math' as math;
import 'package:xoxknit/app/modules/shape_test/controllers/shape_editor_controller.dart';
import 'package:xoxknit/app/modules/shape_test/controllers/managers/knitting_instructions_manager.dart';
import 'package:xoxknit/app/modules/shape_test/models/cell_dimensions.dart';
import 'package:xoxknit/app/modules/new_item/controllers/new_item_wizard_controller.dart';
import 'package:xoxknit/app/modules/shape_test/controllers/zone_creation_controller.dart';

class ZonesEditorController extends GetxController {
  final RxInt selectedZoneIndex = (-1).obs; // -1 means no zone selected
  final RxBool isEditingMode = false.obs;
  final RxBool isCreatingNewZone = false.obs;

  // For creating a new zone
  final RxList<List<bool>> newZoneSelection = <List<bool>>[].obs;

  // For visual feedback
  final RxBool showSuccessOverlay = false.obs;
  final RxString feedbackMessage = ''.obs;

  // For zone creation
  final Map<String, int> pendingZoneBoundingBox = {};

  // Reference to shape controller for access to pattern data
  late final ShapeEditorController shapeController;

  // Reference to wizard controller for saving state
  late final NewItemWizardController wizardController;

  // Reference to zone creation controller for toolbar access
  ZoneCreationController? zoneCreationController;

  // Zone history management getters
  RxBool get canUndoZones =>
      shapeController.knittingInstructionsManager.canUndoZones;
  RxBool get canRedoZones =>
      shapeController.knittingInstructionsManager.canRedoZones;
  Rx<String?> get lastZoneOperation =>
      shapeController.knittingInstructionsManager.lastZoneOperation;

  ZonesEditorController() {
    shapeController = Get.find<ShapeEditorController>();

    // Get wizard controller for saving
    if (Get.isRegistered<NewItemWizardController>()) {
      wizardController = Get.find<NewItemWizardController>();
    }
  }

  // Method to set the zone creation controller reference
  void setZoneCreationController(ZoneCreationController controller) {
    zoneCreationController = controller;
  }

  // For handling overlapping zone selection
  Offset? _lastTapPosition;
  DateTime? _lastTapTime;
  List<int> _overlappingZonesAtLastTap = [];
  int _currentOverlapIndex = 0;

  // --- Zone Context Menu State ---
  final Rx<Offset?> initialZoneContextMenuPosition = Rx<Offset?>(null);
  final Rx<Offset?> calculatedZoneContextMenuPosition = Rx<Offset?>(null);
  final RxBool isZoneContextMenuVisible = false.obs;
  final GlobalKey zoneContextMenuKey = GlobalKey();

  // Select a zone by index
  void selectZone(int index) {
    debugPrint('Selecting zone: $index');
    selectedZoneIndex.value = index;
    debugPrint('Selected zone index is now: ${selectedZoneIndex.value}');
    update(); // Force UI update
  }

  // Handle zone selection with overlap detection
  void handleZoneTap(Offset tapPosition, Size canvasSize) {
    final zones =
        shapeController.knittingInstructionsManager.knittingZones.value;
    final instructions =
        shapeController.knittingInstructionsManager.currentInstructions.value;

    if (zones.isEmpty || instructions.isEmpty) return;

    // Find all zones that contain this tap position
    final overlappingZones = _findOverlappingZonesAtPosition(
        tapPosition, canvasSize, zones, instructions);

    if (overlappingZones.isEmpty) {
      // No zone found at this position, deselect
      deselectZone();
      return;
    }

    // Check if this is a repeated tap in the same area (within 3 seconds and 20 pixels)
    final now = DateTime.now();
    final isRepeatedTap = _lastTapPosition != null &&
        _lastTapTime != null &&
        (tapPosition - _lastTapPosition!).distance < 20.0 &&
        now.difference(_lastTapTime!).inSeconds < 3;

    if (isRepeatedTap &&
        _listEquals(overlappingZones, _overlappingZonesAtLastTap)) {
      // Cycle to the next overlapping zone
      _currentOverlapIndex =
          (_currentOverlapIndex + 1) % overlappingZones.length;
      debugPrint(
          'Cycling to next overlapping zone: ${_currentOverlapIndex}/${overlappingZones.length}');
    } else {
      // New tap position or different overlapping zones
      _overlappingZonesAtLastTap = List.from(overlappingZones);
      _currentOverlapIndex = 0;
      debugPrint(
          'New tap position with ${overlappingZones.length} overlapping zones');
    }

    // Update tap tracking
    _lastTapPosition = tapPosition;
    _lastTapTime = now;

    // Select the zone at the current overlap index
    final selectedZone = overlappingZones[_currentOverlapIndex];
    selectZone(selectedZone);

    // Show feedback if multiple zones overlap
    if (overlappingZones.length > 1) {
      final zoneName = zones[selectedZone].name;
      showFeedback(
          'Selected $zoneName (${_currentOverlapIndex + 1}/${overlappingZones.length})');
    }
  }

  // Find all zones that overlap at a given tap position
  List<int> _findOverlappingZonesAtPosition(Offset tapPosition, Size canvasSize,
      List<KnittingZone> zones, List<List<bool>> instructions) {
    final aspectRatio = _calculateAspectRatio();
    final cellDimensions =
        _calculateCellDimensions(canvasSize, instructions, aspectRatio);

    List<int> overlappingZones = [];

    for (int i = 0; i < zones.length; i++) {
      final zone = zones[i];

      // Check if tap is within zone's bounding box
      if (_isPositionInZoneBounds(tapPosition, zone, cellDimensions)) {
        // For better precision, also check if tap is on an actual stitch of the zone
        if (_isPositionOnZoneStitch(
            tapPosition, zone, cellDimensions, instructions)) {
          overlappingZones.add(i);
        }
      }
    }

    // Sort by zone area (smallest first) to prioritize more specific zones
    overlappingZones.sort((a, b) {
      final areaA = _calculateZoneArea(zones[a]);
      final areaB = _calculateZoneArea(zones[b]);
      return areaA.compareTo(areaB);
    });

    return overlappingZones;
  }

  // Check if position is within zone bounds
  bool _isPositionInZoneBounds(
      Offset position, KnittingZone zone, dynamic cellDimensions) {
    final left =
        zone.startNeedle * cellDimensions.cellWidth + cellDimensions.offsetX;
    final top =
        zone.startRow * cellDimensions.cellHeight + cellDimensions.offsetY;
    final right = (zone.endNeedle + 1) * cellDimensions.cellWidth +
        cellDimensions.offsetX;
    final bottom =
        (zone.endRow + 1) * cellDimensions.cellHeight + cellDimensions.offsetY;

    return position.dx >= left &&
        position.dx <= right &&
        position.dy >= top &&
        position.dy <= bottom;
  }

  // Check if position is on an actual stitch of the zone (more precise than bounding box)
  bool _isPositionOnZoneStitch(Offset position, KnittingZone zone,
      dynamic cellDimensions, List<List<bool>> instructions) {
    // Convert tap position to grid coordinates
    final gridCol =
        ((position.dx - cellDimensions.offsetX) / cellDimensions.cellWidth)
            .floor();
    final gridRow =
        ((position.dy - cellDimensions.offsetY) / cellDimensions.cellHeight)
            .floor();

    // Check if the grid position is valid
    if (gridRow < 0 ||
        gridRow >= instructions.length ||
        gridCol < 0 ||
        gridCol >= instructions[0].length) {
      return false;
    }

    // Check if there's actually a stitch at this position in the global pattern
    if (!instructions[gridRow][gridCol]) {
      return false;
    }

    // Convert to zone local coordinates
    final localRow = gridRow - zone.startRow;
    final localCol = gridCol - zone.startNeedle;

    // Check if local coordinates are within zone bounds
    if (localRow < 0 ||
        localRow >= zone.instructions.length ||
        localCol < 0 ||
        localCol >= zone.instructions[localRow].length) {
      return false;
    }

    // Check if this stitch belongs to the zone
    return zone.instructions[localRow][localCol];
  }

  // Calculate zone area for sorting
  int _calculateZoneArea(KnittingZone zone) {
    return (zone.endRow - zone.startRow + 1) *
        (zone.endNeedle - zone.startNeedle + 1);
  }

  // Calculate aspect ratio
  double _calculateAspectRatio() {
    double aspectRatio = 0.75; // Default fallback
    if (Get.isRegistered<NewItemWizardController>()) {
      final wizardController = Get.find<NewItemWizardController>();
      final stitchesPerCm = wizardController.newItem.value.stitchesPerCm;
      final rowsPerCm = wizardController.newItem.value.rowsPerCm;

      if (rowsPerCm != null &&
          rowsPerCm > 0 &&
          stitchesPerCm != null &&
          stitchesPerCm > 0) {
        aspectRatio = stitchesPerCm / rowsPerCm;
      }
    }
    return aspectRatio;
  }

  // Calculate cell dimensions
  dynamic _calculateCellDimensions(
      Size canvasSize, List<List<bool>> instructions, double aspectRatio) {
    // Use the same calculation as in the widgets
    return CellDimensions.calculate(
        canvasSize, instructions[0].length, instructions.length, aspectRatio);
  }

  // Helper to compare lists
  bool _listEquals<T>(List<T> a, List<T> b) {
    if (a.length != b.length) return false;
    for (int i = 0; i < a.length; i++) {
      if (a[i] != b[i]) return false;
    }
    return true;
  }

  // Helper function to generate letter-based zone names (A, B, C, ..., Z, AA, AB, etc.)
  String _generateZoneName(int index) {
    String result = '';
    int columnNumber = index + 1; // Convert 0-based to 1-based

    while (columnNumber > 0) {
      columnNumber--; // Make it 0-based for this iteration
      result = String.fromCharCode(65 + (columnNumber % 26)) + result;
      columnNumber = columnNumber ~/ 26;
    }

    return 'Zone $result';
  }

  // Helper function to check if a name matches the default pattern (Zone A, Zone B, etc.)
  bool isDefaultZoneName(String name) {
    final pattern = RegExp(r'^Zone [A-Z]+$');
    return pattern.hasMatch(name);
  }

  // Method to set a custom name for a zone
  void setZoneCustomName(int zoneIndex, String customName) {
    final zones =
        shapeController.knittingInstructionsManager.knittingZones.value;
    if (zoneIndex >= 0 && zoneIndex < zones.length) {
      // Validate custom name (basic validation)
      if (customName.trim().isEmpty) {
        Get.snackbar(
          'Invalid Name',
          'Zone name cannot be empty.',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.orange,
          colorText: Colors.white,
        );
        return;
      }

      final oldName = zones[zoneIndex].name;

      // Start history tracking for name change
      startZoneHistoryTracking('Rename Zone "$oldName" to "$customName"');

      final updatedZones = List<KnittingZone>.from(zones);
      updatedZones[zoneIndex] =
          zones[zoneIndex].copyWith(name: customName.trim());
      shapeController.knittingInstructionsManager.knittingZones.value =
          updatedZones;

      // Save after updating zone name
      _saveZones("Updated zone name to '$customName'");

      // Finish history tracking
      finishZoneHistoryTracking();

      showFeedback('Zone renamed to "$customName"');
      update(); // Force UI update
    }
  }

  // Method to reset a zone name to its default letter-based name
  void resetZoneToDefaultName(int zoneIndex) {
    final zones =
        shapeController.knittingInstructionsManager.knittingZones.value;
    if (zoneIndex >= 0 && zoneIndex < zones.length) {
      final oldName = zones[zoneIndex].name;
      final defaultName = _generateZoneName(zoneIndex);

      // Start history tracking for name reset
      startZoneHistoryTracking('Reset Zone Name "$oldName" to Default');

      final updatedZones = List<KnittingZone>.from(zones);
      updatedZones[zoneIndex] = zones[zoneIndex].copyWith(name: defaultName);
      shapeController.knittingInstructionsManager.knittingZones.value =
          updatedZones;

      // Save after updating zone name
      _saveZones("Reset zone name to default: '$defaultName'");

      // Finish history tracking
      finishZoneHistoryTracking();

      showFeedback('Zone name reset to "$defaultName"');
      update(); // Force UI update
    }
  }

  // Public method to force zone reordering - useful for external calls
  void forceZoneReordering() {
    debugPrint('Force reordering zones...');
    _updateZoneOrder();
  }

  // Method to ensure zones are always properly ordered whenever zones list is modified
  void _ensureZonesOrdered() {
    // This method can be called after any direct zone list modification
    // to guarantee proper ordering
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _updateZoneOrder();
    });
  }

  // Deselect all zones
  void deselectZone() {
    debugPrint('Deselecting zone (was: ${selectedZoneIndex.value})');
    selectedZoneIndex.value = -1;
    debugPrint('Selected zone index is now: ${selectedZoneIndex.value}');
    update(); // Force UI update
  }

  // Toggle edit mode
  void toggleEditMode() {
    isEditingMode.value = !isEditingMode.value;

    // When exiting edit mode, deselect the current zone
    if (!isEditingMode.value) {
      deselectZone();
      isCreatingNewZone.value = false;
      newZoneSelection.clear();

      // Save zones when exiting edit mode
      _saveZones("Exited edit mode");
    }

    // Force UI update
    update();
  }

  // Start creating a new zone
  void startNewZoneCreation() {
    selectedZoneIndex.value = -1;
    isCreatingNewZone.value = true;

    // Initialize the selection grid for the new zone
    final instructions =
        shapeController.knittingInstructionsManager.currentInstructions.value;
    if (instructions.isNotEmpty && instructions[0].isNotEmpty) {
      final newGrid = List.generate(instructions.length,
          (i) => List.filled(instructions[0].length, false));
      newZoneSelection.value = newGrid;

      // Force UI update
      update();
    } else {
      newZoneSelection.value = []; // Initialize as empty
      Get.snackbar("Pattern Error",
          "Cannot create zone: Pattern data is missing or invalid.",
          snackPosition: SnackPosition.BOTTOM);
      isCreatingNewZone.value = false; // Revert mode
      update();
    }
  }

  // Cancel zone creation
  void cancelZoneCreation() {
    isCreatingNewZone.value = false;
    newZoneSelection.clear();
    pendingZoneBoundingBox.clear();
    update(); // Force UI update
  }

  // Set a cell's selection state for new zone
  void updateSelectionCell(int row, int col, bool selected) {
    if (newZoneSelection.value.isNotEmpty &&
        row >= 0 &&
        row < newZoneSelection.value.length &&
        newZoneSelection.value[row].isNotEmpty &&
        col >= 0 &&
        col < newZoneSelection.value[row].length) {
      // Create a deep copy to ensure GetX detects the change properly
      final List<List<bool>> newGridData = newZoneSelection.value
          .map((innerList) => List<bool>.from(innerList))
          .toList();
      newGridData[row][col] = selected;
      newZoneSelection.value = newGridData;
      update(); // Force UI update
    }
  }

  // Update entire selection grid
  void updateEntireSelection(List<List<bool>> newSelection) {
    newZoneSelection.value = newSelection;
    update(); // Force UI update
  }

  // Delete the selected zone and reallocate its space to adjacent zones
  void deleteSelectedZone() {
    if (selectedZoneIndex.value >= 0) {
      final zones =
          shapeController.knittingInstructionsManager.knittingZones.value;
      if (selectedZoneIndex.value < zones.length) {
        final zoneToDelete = zones[selectedZoneIndex.value];

        // Get the global pattern instructions to know which stitches need reallocation
        final globalInstructions = shapeController
            .knittingInstructionsManager.currentInstructions.value;

        if (globalInstructions.isEmpty) {
          Get.snackbar(
            'Error',
            'Cannot delete zone: Pattern data is not available.',
            snackPosition: SnackPosition.BOTTOM,
            backgroundColor: Colors.red,
            colorText: Colors.white,
          );
          return;
        }

        // Start history tracking for delete operation
        startZoneHistoryTracking('Delete Zone "${zoneToDelete.name}"');

        // Create a list of zones without the one to be deleted
        final updatedZones = List<KnittingZone>.from(zones);
        updatedZones.removeAt(selectedZoneIndex.value);

        // Reallocate the deleted zone's space to adjacent zones
        final reallocatedZones = _reallocateDeletedZoneSpace(
            zoneToDelete, updatedZones, globalInstructions);

        // Sort and update the zones list
        final orderedReallocatedZones =
            _sortZonesByKnittingOrder(reallocatedZones);
        shapeController.knittingInstructionsManager.knittingZones.value =
            orderedReallocatedZones;

        deselectZone(); // Clear selection

        // Ensure zones are properly ordered (redundant but safe)
        _updateZoneOrder();

        // Save after deleting a zone
        _saveZones("Deleted zone and reallocated space");

        // Finish history tracking
        finishZoneHistoryTracking();

        showFeedback('Zone Deleted and Space Reallocated');
        update(); // Force UI update
      }
    }
  }

  // Reallocate the space of a deleted zone to adjacent zones
  List<KnittingZone> _reallocateDeletedZoneSpace(
    KnittingZone deletedZone,
    List<KnittingZone> remainingZones,
    List<List<bool>> globalInstructions,
  ) {
    if (remainingZones.isEmpty) {
      // If no remaining zones, return empty list
      return [];
    }

    // Find all stitches from the deleted zone that need to be reallocated
    List<MapEntry<int, int>> stitchesToReallocate = [];
    for (int localRow = 0;
        localRow < deletedZone.instructions.length;
        localRow++) {
      for (int localCol = 0;
          localCol < deletedZone.instructions[localRow].length;
          localCol++) {
        if (deletedZone.instructions[localRow][localCol]) {
          final globalRow = deletedZone.startRow + localRow;
          final globalCol = deletedZone.startNeedle + localCol;

          // Only reallocate if it's a valid stitch in the global pattern
          if (globalRow >= 0 &&
              globalRow < globalInstructions.length &&
              globalCol >= 0 &&
              globalCol < globalInstructions[globalRow].length &&
              globalInstructions[globalRow][globalCol]) {
            stitchesToReallocate.add(MapEntry(globalRow, globalCol));
          }
        }
      }
    }

    if (stitchesToReallocate.isEmpty) {
      // No stitches to reallocate, return original zones
      return remainingZones;
    }

    // Find the single best zone to receive ALL the deleted zone's stitches
    int bestZoneIndex =
        _findBestZoneForDeletedZone(deletedZone, remainingZones);

    if (bestZoneIndex < 0) {
      // No suitable zone found, return original zones
      debugPrint('No suitable zone found for reallocation');
      return remainingZones;
    }

    // Create updated zones with ALL stitches going to the single best zone
    List<KnittingZone> updatedZones = [];

    for (int zoneIndex = 0; zoneIndex < remainingZones.length; zoneIndex++) {
      final originalZone = remainingZones[zoneIndex];

      if (zoneIndex == bestZoneIndex) {
        // This is the zone that will receive all the deleted stitches
        final updatedZone =
            _expandZoneWithStitches(originalZone, stitchesToReallocate);
        updatedZones.add(updatedZone);
      } else {
        // Keep the zone as is
        updatedZones.add(originalZone);
      }
    }

    // Sort the updated zones before returning
    return _sortZonesByKnittingOrder(updatedZones);
  }

  // Find the best single zone to receive all stitches from a deleted zone
  int _findBestZoneForDeletedZone(
      KnittingZone deletedZone, List<KnittingZone> remainingZones) {
    if (remainingZones.isEmpty) return -1;

    int bestZoneIndex = -1;
    double bestScore = double.negativeInfinity;

    // Calculate the center of the deleted zone
    final deletedCenterRow = (deletedZone.startRow + deletedZone.endRow) / 2;
    final deletedCenterCol =
        (deletedZone.startNeedle + deletedZone.endNeedle) / 2;

    for (int zoneIndex = 0; zoneIndex < remainingZones.length; zoneIndex++) {
      final zone = remainingZones[zoneIndex];

      // Calculate scoring factors
      double score = 0.0;

      // Factor 1: Distance between zone centers (closer is better)
      final zoneCenterRow = (zone.startRow + zone.endRow) / 2;
      final zoneCenterCol = (zone.startNeedle + zone.endNeedle) / 2;
      final distance = math.sqrt(math.pow(deletedCenterRow - zoneCenterRow, 2) +
          math.pow(deletedCenterCol - zoneCenterCol, 2));

      // Invert distance so closer zones get higher scores
      final distanceScore = distance > 0 ? 1000.0 / distance : 1000.0;

      // Factor 2: Check if zones are adjacent (sharing boundaries) - big bonus
      bool isAdjacent = _areZonesAdjacent(deletedZone, zone);
      final adjacencyScore = isAdjacent ? 2000.0 : 0.0;

      // Factor 3: Size compatibility - prefer zones of similar size
      final deletedSize = (deletedZone.endRow - deletedZone.startRow + 1) *
          (deletedZone.endNeedle - deletedZone.startNeedle + 1);
      final zoneSize = (zone.endRow - zone.startRow + 1) *
          (zone.endNeedle - zone.startNeedle + 1);

      // Prefer zones that are not too small compared to the deleted zone
      final sizeRatio = zoneSize / deletedSize;
      final sizeScore = sizeRatio >= 0.5 ? 100.0 : sizeRatio * 200.0;

      // Combine all factors
      score = adjacencyScore + distanceScore + sizeScore;

      if (score > bestScore) {
        bestScore = score;
        bestZoneIndex = zoneIndex;
      }
    }

    return bestZoneIndex;
  }

  // Check if two zones are adjacent (share boundaries)
  bool _areZonesAdjacent(KnittingZone zone1, KnittingZone zone2) {
    // Check horizontal adjacency (zones are next to each other horizontally)
    bool horizontallyAdjacent = (zone1.endNeedle + 1 == zone2.startNeedle ||
            zone2.endNeedle + 1 == zone1.startNeedle) &&
        !(zone1.endRow < zone2.startRow || zone1.startRow > zone2.endRow);

    // Check vertical adjacency (zones are next to each other vertically)
    bool verticallyAdjacent = (zone1.endRow + 1 == zone2.startRow ||
            zone2.endRow + 1 == zone1.startRow) &&
        !(zone1.endNeedle < zone2.startNeedle ||
            zone1.startNeedle > zone2.endNeedle);

    return horizontallyAdjacent || verticallyAdjacent;
  }

  // Find the closest zone to a given stitch position
  int _findClosestZone(int globalRow, int globalCol, List<KnittingZone> zones) {
    if (zones.isEmpty) return -1;

    int closestZoneIndex = -1;
    double minDistance = double.infinity;

    for (int zoneIndex = 0; zoneIndex < zones.length; zoneIndex++) {
      final zone = zones[zoneIndex];

      // Calculate distance to the zone's center
      final zoneCenterRow = (zone.startRow + zone.endRow) / 2;
      final zoneCenterCol = (zone.startNeedle + zone.endNeedle) / 2;

      final distance = math.sqrt(math.pow(globalRow - zoneCenterRow, 2) +
          math.pow(globalCol - zoneCenterCol, 2));

      // Prefer zones that are adjacent (sharing boundaries)
      bool isAdjacent = _isAdjacentToZone(globalRow, globalCol, zone);

      // Give priority to adjacent zones by reducing their effective distance
      final effectiveDistance = isAdjacent ? distance * 0.5 : distance;

      if (effectiveDistance < minDistance) {
        minDistance = effectiveDistance;
        closestZoneIndex = zoneIndex;
      }
    }

    return closestZoneIndex;
  }

  // Check if a stitch position is adjacent to a zone
  bool _isAdjacentToZone(int globalRow, int globalCol, KnittingZone zone) {
    // Check if the stitch is within one position of the zone's bounding box
    bool horizontallyAdjacent =
        (globalCol >= zone.startNeedle - 1 && globalCol <= zone.endNeedle + 1);
    bool verticallyAdjacent =
        (globalRow >= zone.startRow - 1 && globalRow <= zone.endRow + 1);

    // Consider it adjacent if it's next to the zone boundary
    bool nextToHorizontalBoundary = (globalCol == zone.startNeedle - 1 ||
            globalCol == zone.endNeedle + 1) &&
        (globalRow >= zone.startRow && globalRow <= zone.endRow);
    bool nextToVerticalBoundary =
        (globalRow == zone.startRow - 1 || globalRow == zone.endRow + 1) &&
            (globalCol >= zone.startNeedle && globalCol <= zone.endNeedle);

    return nextToHorizontalBoundary || nextToVerticalBoundary;
  }

  // Expand a zone to include additional stitches
  KnittingZone _expandZoneWithStitches(
      KnittingZone originalZone, List<MapEntry<int, int>> additionalStitches) {
    if (additionalStitches.isEmpty) {
      return originalZone;
    }

    // Calculate the new bounding box including additional stitches
    int newStartRow = originalZone.startRow;
    int newEndRow = originalZone.endRow;
    int newStartNeedle = originalZone.startNeedle;
    int newEndNeedle = originalZone.endNeedle;

    for (final stitch in additionalStitches) {
      newStartRow = math.min(newStartRow, stitch.key);
      newEndRow = math.max(newEndRow, stitch.key);
      newStartNeedle = math.min(newStartNeedle, stitch.value);
      newEndNeedle = math.max(newEndNeedle, stitch.value);
    }

    // Create new instructions grid with the expanded dimensions
    final newWidth = newEndNeedle - newStartNeedle + 1;
    final newHeight = newEndRow - newStartRow + 1;

    List<List<bool>> newInstructions =
        List.generate(newHeight, (_) => List.filled(newWidth, false));

    // Copy existing stitches from the original zone
    for (int localRow = 0;
        localRow < originalZone.instructions.length;
        localRow++) {
      for (int localCol = 0;
          localCol < originalZone.instructions[localRow].length;
          localCol++) {
        if (originalZone.instructions[localRow][localCol]) {
          final globalRow = originalZone.startRow + localRow;
          final globalCol = originalZone.startNeedle + localCol;

          // Convert to new local coordinates
          final newLocalRow = globalRow - newStartRow;
          final newLocalCol = globalCol - newStartNeedle;

          if (newLocalRow >= 0 &&
              newLocalRow < newHeight &&
              newLocalCol >= 0 &&
              newLocalCol < newWidth) {
            newInstructions[newLocalRow][newLocalCol] = true;
          }
        }
      }
    }

    // Add the additional stitches
    for (final stitch in additionalStitches) {
      final newLocalRow = stitch.key - newStartRow;
      final newLocalCol = stitch.value - newStartNeedle;

      if (newLocalRow >= 0 &&
          newLocalRow < newHeight &&
          newLocalCol >= 0 &&
          newLocalCol < newWidth) {
        newInstructions[newLocalRow][newLocalCol] = true;
      }
    }

    return originalZone.copyWith(
      instructions: newInstructions,
      startRow: newStartRow,
      endRow: newEndRow,
      startNeedle: newStartNeedle,
      endNeedle: newEndNeedle,
    );
  }

  // Update a zone boundary
  void updateZoneBoundary(
      int zoneIndex, String edge, int delta, bool isNeeddleChange) {
    final zones =
        shapeController.knittingInstructionsManager.knittingZones.value;
    if (zoneIndex < 0 || zoneIndex >= zones.length) return;

    final targetZone = zones[zoneIndex];

    // Start history tracking for boundary update
    startZoneHistoryTracking('Resize Zone "${targetZone.name}" ($edge edge)');

    switch (edge) {
      case 'left':
        if (isNeeddleChange) {
          // Adjust startNeedle (left edge)
          final newStartNeedle = targetZone.startNeedle + delta;
          if (newStartNeedle < 0 || newStartNeedle >= targetZone.endNeedle) {
            cancelZoneHistoryTracking();
            return;
          }
          _updateZoneLeftEdge(targetZone, newStartNeedle);
        }
        break;
      case 'right':
        if (isNeeddleChange) {
          // Adjust endNeedle (right edge)
          final newEndNeedle = targetZone.endNeedle + delta;
          if (newEndNeedle <= targetZone.startNeedle) {
            cancelZoneHistoryTracking();
            return;
          }
          _updateZoneRightEdge(targetZone, newEndNeedle);
        }
        break;
      case 'top':
        if (!isNeeddleChange) {
          // Adjust startRow (top edge)
          final newStartRow = targetZone.startRow + delta;
          if (newStartRow < 0 || newStartRow >= targetZone.endRow) {
            cancelZoneHistoryTracking();
            return;
          }
          _updateZoneTopEdge(targetZone, newStartRow);
        }
        break;
      case 'bottom':
        if (!isNeeddleChange) {
          // Adjust endRow (bottom edge)
          final newEndRow = targetZone.endRow + delta;
          if (newEndRow <= targetZone.startRow) {
            cancelZoneHistoryTracking();
            return;
          }
          _updateZoneBottomEdge(targetZone, newEndRow);
        }
        break;
    }

    // Save after updating zone boundaries
    _saveZones("Updated zone boundary");

    // Update zone order after boundary changes
    _updateZoneOrder();

    // Finish history tracking
    finishZoneHistoryTracking();

    update(); // Force UI update
  }

  // Helper methods for updating zone edges
  void _updateZoneLeftEdge(KnittingZone zone, int newStartNeedle) {
    final oldStartNeedle = zone.startNeedle;
    final delta = newStartNeedle - oldStartNeedle;

    if (delta == 0) return; // No change

    List<List<bool>> newInstructions = List.from(zone.instructions);

    if (delta > 0) {
      // Shrinking from left - remove columns from left
      newInstructions =
          newInstructions.map((row) => row.sublist(delta, row.length)).toList();
    } else {
      // Expanding to left - add empty columns to left
      final colsToAdd = -delta;
      newInstructions = newInstructions
          .map((row) => List<bool>.filled(colsToAdd, false)..addAll(row))
          .toList();
    }

    // Use copyWith to create a new zone with updated properties
    final updatedZone = zone.copyWith(
      instructions: newInstructions,
      startNeedle: newStartNeedle,
    );

    // Update the zone in the list by replacing it
    final zones =
        shapeController.knittingInstructionsManager.knittingZones.value;
    final zoneIndex = zones.indexOf(zone);
    if (zoneIndex >= 0) {
      final updatedZones = List<KnittingZone>.from(zones);
      updatedZones[zoneIndex] = updatedZone;
      shapeController.knittingInstructionsManager.knittingZones.value =
          updatedZones;
    }
  }

  void _updateZoneRightEdge(KnittingZone zone, int newEndNeedle) {
    final oldEndNeedle = zone.endNeedle;
    final delta = newEndNeedle - oldEndNeedle;

    if (delta == 0) return; // No change

    List<List<bool>> newInstructions = List.from(zone.instructions);

    if (delta < 0) {
      // Shrinking from right - remove columns from right
      final colsToRemove = -delta;
      newInstructions = newInstructions
          .map((row) => row.sublist(0, row.length - colsToRemove))
          .toList();
    } else {
      // Expanding to right - add empty columns to right
      final colsToAdd = delta;
      newInstructions = newInstructions
          .map((row) => [...row, ...List<bool>.filled(colsToAdd, false)])
          .toList();
    }

    // Use copyWith to create a new zone with updated properties
    final updatedZone = zone.copyWith(
      instructions: newInstructions,
      endNeedle: newEndNeedle,
    );

    // Update the zone in the list by replacing it
    final zones =
        shapeController.knittingInstructionsManager.knittingZones.value;
    final zoneIndex = zones.indexOf(zone);
    if (zoneIndex >= 0) {
      final updatedZones = List<KnittingZone>.from(zones);
      updatedZones[zoneIndex] = updatedZone;
      shapeController.knittingInstructionsManager.knittingZones.value =
          updatedZones;
    }
  }

  void _updateZoneTopEdge(KnittingZone zone, int newStartRow) {
    final oldStartRow = zone.startRow;
    final delta = newStartRow - oldStartRow;

    if (delta == 0) return; // No change

    List<List<bool>> newInstructions = List.from(zone.instructions);

    if (delta > 0) {
      // Shrinking from top - remove rows from top
      newInstructions = newInstructions.sublist(delta);
    } else {
      // Expanding to top - add empty rows to top
      final rowsToAdd = -delta;
      final emptyRows = List.generate(rowsToAdd,
          (_) => List<bool>.filled(newInstructions[0].length, false));
      newInstructions = [...emptyRows, ...newInstructions];
    }

    // Use copyWith to create a new zone with updated properties
    final updatedZone = zone.copyWith(
      instructions: newInstructions,
      startRow: newStartRow,
    );

    // Update the zone in the list by replacing it
    final zones =
        shapeController.knittingInstructionsManager.knittingZones.value;
    final zoneIndex = zones.indexOf(zone);
    if (zoneIndex >= 0) {
      final updatedZones = List<KnittingZone>.from(zones);
      updatedZones[zoneIndex] = updatedZone;
      shapeController.knittingInstructionsManager.knittingZones.value =
          updatedZones;
    }
  }

  void _updateZoneBottomEdge(KnittingZone zone, int newEndRow) {
    final oldEndRow = zone.endRow;
    final delta = newEndRow - oldEndRow;

    if (delta == 0) return; // No change

    List<List<bool>> newInstructions = List.from(zone.instructions);

    if (delta < 0) {
      // Shrinking from bottom - remove rows from bottom
      final rowsToRemove = -delta;
      newInstructions =
          newInstructions.sublist(0, newInstructions.length - rowsToRemove);
    } else {
      // Expanding to bottom - add empty rows to bottom
      final rowsToAdd = delta;
      final emptyRows = List.generate(rowsToAdd,
          (_) => List<bool>.filled(newInstructions[0].length, false));
      newInstructions = [...newInstructions, ...emptyRows];
    }

    // Use copyWith to create a new zone with updated properties
    final updatedZone = zone.copyWith(
      instructions: newInstructions,
      endRow: newEndRow,
    );

    // Update the zone in the list by replacing it
    final zones =
        shapeController.knittingInstructionsManager.knittingZones.value;
    final zoneIndex = zones.indexOf(zone);
    if (zoneIndex >= 0) {
      final updatedZones = List<KnittingZone>.from(zones);
      updatedZones[zoneIndex] = updatedZone;
      shapeController.knittingInstructionsManager.knittingZones.value =
          updatedZones;
    }
  }

  // Save the new zone from polygon selection
  void saveNewZone() {
    // Check if we have any selected stitches
    bool hasSelection = false;
    for (final row in newZoneSelection) {
      if (row.contains(true)) {
        hasSelection = true;
        break;
      }
    }

    if (!hasSelection) {
      Get.snackbar(
        'No Selection',
        'Please select at least one stitch for the new zone.',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.orange,
        colorText: Colors.white,
      );
      return;
    }

    // Start history tracking for create zone operation
    startZoneHistoryTracking('Create New Zone');

    // Use bounding box from polygon selection if available
    int minRow, maxRow, minCol, maxCol;

    if (pendingZoneBoundingBox.isNotEmpty) {
      // Use the saved bounding box from polygon selection
      minRow = pendingZoneBoundingBox['minRow']!;
      maxRow = pendingZoneBoundingBox['maxRow']!;
      minCol = pendingZoneBoundingBox['minCol']!;
      maxCol = pendingZoneBoundingBox['maxCol']!;
    } else {
      // Fall back to calculating from selection
      final instructions =
          shapeController.knittingInstructionsManager.currentInstructions.value;
      minRow = instructions.length;
      maxRow = 0;
      minCol = instructions[0].length;
      maxCol = 0;

      for (int r = 0; r < newZoneSelection.length; r++) {
        for (int c = 0; c < newZoneSelection[r].length; c++) {
          if (newZoneSelection[r][c]) {
            minRow = math.min(minRow, r);
            maxRow = math.max(maxRow, r);
            minCol = math.min(minCol, c);
            maxCol = math.max(maxCol, c);
          }
        }
      }
    }

    // Verify the bounds are valid
    if (minRow > maxRow || minCol > maxCol) {
      Get.snackbar(
        'Invalid Zone',
        'Could not determine valid zone boundaries.',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return;
    }

    // Create the local instructions from the global selection
    List<List<bool>> newZoneLocalInstructions = [];
    int width = maxCol - minCol + 1;
    int height = maxRow - minRow + 1;

    // Initialize with all false
    for (int r = 0; r < height; r++) {
      List<bool> localRow = List<bool>.filled(width, false);
      newZoneLocalInstructions.add(localRow);
    }

    // Copy only the selected stitches within the bounding box
    for (int r = minRow; r <= maxRow; r++) {
      for (int c = minCol; c <= maxCol; c++) {
        // Convert to local coordinates
        int localR = r - minRow;
        int localC = c - minCol;

        // Only copy selected stitches
        if (r < newZoneSelection.length &&
            c < newZoneSelection[r].length &&
            newZoneSelection[r][c]) {
          newZoneLocalInstructions[localR][localC] = true;
        }
      }
    }

    // Get current zones and determine new zone name using letters
    final zones =
        shapeController.knittingInstructionsManager.knittingZones.value;
    final zoneName = _generateZoneName(zones.length);

    final newZone = KnittingZone(
      name: zoneName,
      instructions: newZoneLocalInstructions,
      startNeedle: minCol, // Global column start
      endNeedle: maxCol, // Global column end
      startRow: minRow, // Global row start
      endRow: maxRow, // Global row end
    );

    // Check for overlapping zones and update them
    final updatedZones = _updateOverlappingZones(
      List<KnittingZone>.from(zones),
      newZone,
      newZoneSelection,
    );

    // Add the new zone
    updatedZones.add(newZone);

    // Sort zones before setting them
    final orderedZones = _sortZonesByKnittingOrder(updatedZones);
    shapeController.knittingInstructionsManager.knittingZones.value =
        orderedZones;

    // Exit creation mode
    isCreatingNewZone.value = false;
    newZoneSelection.clear();
    pendingZoneBoundingBox.clear(); // Clear the bounding box

    // Ensure zones are properly ordered (redundant but safe)
    _updateZoneOrder();

    // Reselect the new zone by its position (which may have changed due to sorting)
    final sortedZones =
        shapeController.knittingInstructionsManager.knittingZones.value;
    for (int i = 0; i < sortedZones.length; i++) {
      if (sortedZones[i].startNeedle == newZone.startNeedle &&
          sortedZones[i].endNeedle == newZone.endNeedle &&
          sortedZones[i].startRow == newZone.startRow &&
          sortedZones[i].endRow == newZone.endRow) {
        selectedZoneIndex.value = i;
        break;
      }
    }

    // Save after creating a new zone
    _saveZones("Created new zone");

    // Finish history tracking
    finishZoneHistoryTracking();

    // Show success feedback
    showFeedback('Zone Created Successfully');

    // Force UI update
    update();
  }

  // Update existing zones when a new zone overlaps with them
  List<KnittingZone> _updateOverlappingZones(
    List<KnittingZone> existingZones,
    KnittingZone newZone,
    List<List<bool>> newZoneSelection,
  ) {
    List<KnittingZone> updatedZones = [];

    // For each existing zone, check if it overlaps with the new zone
    for (final zone in existingZones) {
      bool hasOverlap = _checkForOverlap(zone, newZone);

      if (!hasOverlap) {
        // If no overlap, keep zone as is
        updatedZones.add(zone);
        continue;
      }

      // Check if the zone is completely covered by the new zone
      if (_isZoneCompletelyContained(zone, newZoneSelection)) {
        // Zone is completely covered, skip it (effectively removing it)
        debugPrint(
            'Zone ${zone.name} is completely covered by new zone and will be removed');
        continue;
      }

      // Zone partially overlaps, create updated version with overlapping stitches removed
      final updatedZone = _removeOverlappingStitches(zone, newZoneSelection);

      // Only add the zone if it still has stitches after removing overlap
      if (_zoneHasStitches(updatedZone)) {
        updatedZones.add(updatedZone);
      } else {
        debugPrint(
            'Zone ${zone.name} has no stitches left after overlap removal');
      }
    }

    // Sort the updated zones before returning to maintain proper order
    return _sortZonesByKnittingOrder(updatedZones);
  }

  // Check if two zones overlap
  bool _checkForOverlap(KnittingZone zone1, KnittingZone zone2) {
    // Check if bounding boxes overlap
    bool horizontalOverlap = !(zone1.endNeedle < zone2.startNeedle ||
        zone1.startNeedle > zone2.endNeedle);
    bool verticalOverlap =
        !(zone1.endRow < zone2.startRow || zone1.startRow > zone2.endRow);

    return horizontalOverlap && verticalOverlap;
  }

  // Check if a zone is completely contained within the new selection
  bool _isZoneCompletelyContained(
      KnittingZone zone, List<List<bool>> newZoneSelection) {
    // For each stitch in the zone, check if it's also in the new selection
    for (int localRow = 0; localRow < zone.instructions.length; localRow++) {
      for (int localCol = 0;
          localCol < zone.instructions[localRow].length;
          localCol++) {
        // Skip stitches that aren't part of the zone
        if (!zone.instructions[localRow][localCol]) continue;

        // Convert to global coordinates
        int globalRow = zone.startRow + localRow;
        int globalCol = zone.startNeedle + localCol;

        // Check if this stitch is NOT selected in the new zone
        if (globalRow < newZoneSelection.length &&
            globalCol < newZoneSelection[globalRow].length) {
          if (!newZoneSelection[globalRow][globalCol]) {
            // Found a stitch that's not covered by the new selection
            return false;
          }
        } else {
          // Stitch is outside the selection bounds
          return false;
        }
      }
    }

    // If we get here, all stitches are covered by the new selection
    return true;
  }

  // Remove overlapping stitches from an existing zone
  KnittingZone _removeOverlappingStitches(
      KnittingZone existingZone, List<List<bool>> newZoneSelection) {
    // Create a copy of the zone's instructions
    List<List<bool>> currentZoneInstructions =
        List.from(existingZone.instructions.map((row) => List<bool>.from(row)));

    // For each stitch in the existing zone, check if it's selected in the new zone
    for (int localRow = 0;
        localRow < currentZoneInstructions.length;
        localRow++) {
      for (int localCol = 0;
          localCol < currentZoneInstructions[localRow].length;
          localCol++) {
        // Skip stitches that are already false (not part of the zone)
        if (!currentZoneInstructions[localRow][localCol]) continue;

        // Convert local coordinates to global coordinates
        int globalRow = existingZone.startRow + localRow;
        int globalCol = existingZone.startNeedle + localCol;

        // Check if this stitch is selected in the new zone
        if (globalRow >= 0 &&
            globalRow < newZoneSelection.length &&
            globalCol >= 0 &&
            globalCol < newZoneSelection[globalRow].length &&
            newZoneSelection[globalRow][globalCol]) {
          // This stitch overlaps, remove it from the existing zone
          currentZoneInstructions[localRow][localCol] = false;
        }
      }
    }

    // Recalculate bounding box and trim instructions
    int minLocalRow = currentZoneInstructions.length;
    int maxLocalRow = -1;
    int minLocalCol = currentZoneInstructions.isNotEmpty
        ? currentZoneInstructions[0].length
        : 0;
    int maxLocalCol = -1;
    bool hasStitches = false;

    for (int r = 0; r < currentZoneInstructions.length; r++) {
      for (int c = 0; c < currentZoneInstructions[r].length; c++) {
        if (currentZoneInstructions[r][c]) {
          hasStitches = true;
          minLocalRow = math.min(minLocalRow, r);
          maxLocalRow = math.max(maxLocalRow, r);
          minLocalCol = math.min(minLocalCol, c);
          maxLocalCol = math.max(maxLocalCol, c);
        }
      }
    }

    if (!hasStitches) {
      // If no stitches are left, return an empty zone (which will be filtered out)
      return existingZone.copyWith(instructions: [[]]);
    }

    // Create the new trimmed instructions
    List<List<bool>> trimmedInstructions = [];
    for (int r = minLocalRow; r <= maxLocalRow; r++) {
      trimmedInstructions.add(
          currentZoneInstructions[r].sublist(minLocalCol, maxLocalCol + 1));
    }

    // Calculate new global bounding box
    final newGlobalStartRow = existingZone.startRow + minLocalRow;
    final newGlobalEndRow = existingZone.startRow + maxLocalRow;
    final newGlobalStartNeedle = existingZone.startNeedle + minLocalCol;
    final newGlobalEndNeedle = existingZone.startNeedle + maxLocalCol;

    // Create a new zone with the updated instructions and bounding box
    return existingZone.copyWith(
      instructions: trimmedInstructions,
      startRow: newGlobalStartRow,
      endRow: newGlobalEndRow,
      startNeedle: newGlobalStartNeedle,
      endNeedle: newGlobalEndNeedle,
    );
  }

  // Check if a zone has any stitches left
  bool _zoneHasStitches(KnittingZone zone) {
    for (final row in zone.instructions) {
      if (row.contains(true)) {
        return true;
      }
    }
    return false;
  }

  // Sort zones from bottom right to top left
  // This is important for knitting order: we start from bottom and go towards top
  List<KnittingZone> _sortZonesByKnittingOrder(List<KnittingZone> zones) {
    // Make a copy of the zones to avoid modifying the original list
    final sortedZones = List<KnittingZone>.from(zones);

    // Sort by row (bottom to top) and then by needle (right to left)
    sortedZones.sort((a, b) {
      // First sort by bottom row (endRow) in descending order (bottom to top)
      final rowComparison = b.endRow.compareTo(a.endRow);
      if (rowComparison != 0) {
        return rowComparison;
      }

      // For zones at the same vertical level, sort by rightmost needle (endNeedle) in descending order
      return b.endNeedle.compareTo(a.endNeedle);
    });

    // Rename zones to reflect their new order (only for default names, preserve custom names)
    for (int i = 0; i < sortedZones.length; i++) {
      // Only rename zones that have default names, preserve custom names
      if (isDefaultZoneName(sortedZones[i].name)) {
        sortedZones[i] = sortedZones[i].copyWith(name: _generateZoneName(i));
      }
    }

    return sortedZones;
  }

  // Update zone order and persist changes - preserves selection
  void _updateZoneOrder() {
    final zones =
        shapeController.knittingInstructionsManager.knittingZones.value;

    if (zones.isEmpty) return;

    // Store the currently selected zone info before reordering
    KnittingZone? currentlySelectedZone;
    if (selectedZoneIndex.value >= 0 &&
        selectedZoneIndex.value < zones.length) {
      currentlySelectedZone = zones[selectedZoneIndex.value];
    }

    final sortedZones = _sortZonesByKnittingOrder(zones);

    // Only update if the order has changed
    if (!_areZonesInSameOrder(zones, sortedZones)) {
      shapeController.knittingInstructionsManager.knittingZones.value =
          sortedZones;

      // Find and restore the selected zone after reordering
      if (currentlySelectedZone != null) {
        for (int i = 0; i < sortedZones.length; i++) {
          if (_areZonesEqual(sortedZones[i], currentlySelectedZone)) {
            selectedZoneIndex.value = i;
            break;
          }
        }
      }

      _saveZones("Reordered zones");
      update();
      debugPrint('Zones reordered - new count: ${sortedZones.length}');
    }
  }

  // Helper method to check if two zones are the same (by position and size)
  bool _areZonesEqual(KnittingZone zone1, KnittingZone zone2) {
    return zone1.startRow == zone2.startRow &&
        zone1.endRow == zone2.endRow &&
        zone1.startNeedle == zone2.startNeedle &&
        zone1.endNeedle == zone2.endNeedle;
  }

  // Check if two zone lists have the same order
  bool _areZonesInSameOrder(
      List<KnittingZone> zones1, List<KnittingZone> zones2) {
    if (zones1.length != zones2.length) return false;

    for (int i = 0; i < zones1.length; i++) {
      if (zones1[i].startNeedle != zones2[i].startNeedle ||
          zones1[i].endNeedle != zones2[i].endNeedle ||
          zones1[i].startRow != zones2[i].startRow ||
          zones1[i].endRow != zones2[i].endRow) {
        return false;
      }
    }

    return true;
  }

  // Show feedback to the user
  void showFeedback(String message) {
    feedbackMessage.value = message;
    showSuccessOverlay.value = true;

    // Auto-hide after 2 seconds
    Future.delayed(const Duration(seconds: 2), () {
      showSuccessOverlay.value = false;
      update(); // Force UI update to hide overlay
    });
  }

  // Save zones to the wizard state
  void _saveZones(String reason) {
    if (Get.isRegistered<NewItemWizardController>()) {
      debugPrint('Saving zones: $reason');

      try {
        // Save zones using the instruction manager's save method
        shapeController.knittingInstructionsManager.saveZonesToWizardState();

        // Also save the updated zone data to the wizard state
        Get.find<NewItemWizardController>().saveWizardProgress();
      } catch (e) {
        debugPrint('Error saving zones: $e');
      }
    } else {
      debugPrint('Cannot save zones: NewItemWizardController not registered');
    }
  }

  // Public method to save zones
  void saveZones(String reason) {
    _saveZones(reason);
  }

  // --- Zone History Management Methods ---

  /// Start tracking a zone operation for history
  void startZoneHistoryTracking(String operationName) {
    final currentZones =
        shapeController.knittingInstructionsManager.knittingZones.value;
    final currentSelection =
        selectedZoneIndex.value >= 0 ? selectedZoneIndex.value : null;

    shapeController.knittingInstructionsManager.zoneHistoryManager
        .startTracking(currentZones, currentSelection, operationName);

    debugPrint('[ZonesEditorController] Started tracking: $operationName');
  }

  /// Finish tracking a zone operation for history
  void finishZoneHistoryTracking() {
    final newZones =
        shapeController.knittingInstructionsManager.knittingZones.value;
    final newSelection =
        selectedZoneIndex.value >= 0 ? selectedZoneIndex.value : null;

    shapeController.knittingInstructionsManager.zoneHistoryManager
        .finishTracking(newZones, newSelection);

    debugPrint('[ZonesEditorController] Finished tracking');
  }

  /// Cancel current zone history tracking
  void cancelZoneHistoryTracking() {
    shapeController.knittingInstructionsManager.zoneHistoryManager
        .cancelTracking();
    debugPrint('[ZonesEditorController] Cancelled tracking');
  }

  /// Add a zone history entry directly (for operations that don't need tracking)
  void addZoneHistoryEntry(String operationName) {
    final zones =
        shapeController.knittingInstructionsManager.knittingZones.value;
    final selection =
        selectedZoneIndex.value >= 0 ? selectedZoneIndex.value : null;

    shapeController.knittingInstructionsManager.zoneHistoryManager
        .addHistoryEntry(zones, selection, operationName);

    debugPrint('[ZonesEditorController] Added history entry: $operationName');
  }

  /// Undo the last zone operation
  void undoZoneOperation() {
    final historyManager =
        shapeController.knittingInstructionsManager.zoneHistoryManager;
    final previousEntry = historyManager.undo();

    if (previousEntry != null) {
      _applyZoneHistoryEntry(previousEntry);
      showFeedback('Undone: ${previousEntry.operationName ?? 'Operation'}');
    } else {
      // Handle initial state (no more history)
      _resetToInitialZoneState();
      showFeedback('Undone: Back to initial state');
    }

    debugPrint('[ZonesEditorController] Undo completed');
  }

  /// Redo the previously undone zone operation
  void redoZoneOperation() {
    final historyManager =
        shapeController.knittingInstructionsManager.zoneHistoryManager;
    final redoEntry = historyManager.redo();

    if (redoEntry != null) {
      _applyZoneHistoryEntry(redoEntry);
      showFeedback('Redone: ${redoEntry.operationName ?? 'Operation'}');
    }

    debugPrint('[ZonesEditorController] Redo completed');
  }

  /// Apply a zone history entry to restore state
  void _applyZoneHistoryEntry(ZoneHistoryEntry entry) {
    // Restore zones
    shapeController.knittingInstructionsManager.knittingZones.value =
        entry.zones.map((z) => z.copyWith()).toList();

    // Restore selection
    if (entry.selectedZoneIndex != null &&
        entry.selectedZoneIndex! >= 0 &&
        entry.selectedZoneIndex! < entry.zones.length) {
      selectedZoneIndex.value = entry.selectedZoneIndex!;
    } else {
      selectedZoneIndex.value = -1;
    }

    // Save the restored state
    _saveZones("Restored from history: ${entry.operationName ?? 'Unknown'}");

    // Force UI update
    update();
  }

  /// Reset to initial zone state (when undoing reaches the beginning)
  void _resetToInitialZoneState() {
    // Clear all zones
    shapeController.knittingInstructionsManager.knittingZones.value = [];
    selectedZoneIndex.value = -1;

    // Regenerate zones from scratch
    shapeController.knittingInstructionsManager.knittingZones.value =
        shapeController.knittingInstructionsManager.getKnittingZones();

    // Save the reset state
    _saveZones("Reset to initial zone state");

    // Force UI update
    update();
  }

  /// Clear zone history (useful when loading new instructions)
  void clearZoneHistory() {
    shapeController.knittingInstructionsManager.zoneHistoryManager
        .clearHistory();
    debugPrint('[ZonesEditorController] Zone history cleared');
  }

  // --- Zone Context Menu Methods ---

  // Show context menu at the specified position
  void showZoneContextMenu(Offset globalPosition) {
    initialZoneContextMenuPosition.value = globalPosition;
    calculatedZoneContextMenuPosition.value = globalPosition;
    isZoneContextMenuVisible.value = true;
    update();
  }

  // Hide context menu
  void hideZoneContextMenu() {
    if (isZoneContextMenuVisible.value) {
      isZoneContextMenuVisible.value = false;
      initialZoneContextMenuPosition.value = null;
      calculatedZoneContextMenuPosition.value = null;
      update();
    }
  }

  // Calculate and update the menu position to stay within bounds
  void calculateAndSetZoneMenuPosition(BuildContext context) {
    if (!isZoneContextMenuVisible.value ||
        initialZoneContextMenuPosition.value == null ||
        zoneContextMenuKey.currentContext == null) {
      return;
    }

    final RenderBox? menuRenderBox =
        zoneContextMenuKey.currentContext!.findRenderObject() as RenderBox?;
    if (menuRenderBox == null) return;

    final Size menuSize = menuRenderBox.size;
    final Size screenSize = MediaQuery.of(context).size;
    final Offset initialPosition = initialZoneContextMenuPosition.value!;

    double finalLeft = initialPosition.dx;
    double finalTop = initialPosition.dy;
    const double padding = 8.0;

    // Adjust horizontally
    if (finalLeft + menuSize.width > screenSize.width - padding) {
      finalLeft = screenSize.width - menuSize.width - padding;
    }
    if (finalLeft < padding) {
      finalLeft = padding;
    }

    // Adjust vertically
    if (finalTop + menuSize.height > screenSize.height - padding) {
      double topAbove = initialPosition.dy - menuSize.height - padding;
      if (topAbove >= padding) {
        finalTop = topAbove;
      } else {
        finalTop = screenSize.height - menuSize.height - padding;
      }
    }
    if (finalTop < padding) {
      finalTop = padding;
    }

    final Offset newPosition = Offset(finalLeft, finalTop);

    if (calculatedZoneContextMenuPosition.value != newPosition) {
      calculatedZoneContextMenuPosition.value = newPosition;
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (isZoneContextMenuVisible.value) {
          update();
        }
      });
    }
  }

  // Helper method to check if a tap is inside the context menu bounds
  bool isTapInsideZoneContextMenu(Offset globalTapPosition) {
    if (!isZoneContextMenuVisible.value ||
        calculatedZoneContextMenuPosition.value == null ||
        zoneContextMenuKey.currentContext == null) {
      return false;
    }
    final RenderBox? renderBox =
        zoneContextMenuKey.currentContext!.findRenderObject() as RenderBox?;
    if (renderBox == null) return false;

    final menuPosition = calculatedZoneContextMenuPosition.value!;
    final menuSize = renderBox.size;
    final menuRect = Rect.fromLTWH(
        menuPosition.dx, menuPosition.dy, menuSize.width, menuSize.height);

    return menuRect.contains(globalTapPosition);
  }

  // Show zone rename dialog
  void showZoneRenameDialog() {
    if (selectedZoneIndex.value < 0) return;

    final zones =
        shapeController.knittingInstructionsManager.knittingZones.value;
    if (selectedZoneIndex.value >= zones.length) return;

    final currentZone = zones[selectedZoneIndex.value];
    final TextEditingController textController =
        TextEditingController(text: currentZone.name);

    Get.dialog(
      AlertDialog(
        title: const Text('Rename Zone'),
        content: TextField(
          controller: textController,
          decoration: const InputDecoration(
            labelText: 'Zone Name',
            hintText: 'Enter new zone name',
          ),
          autofocus: true,
          onSubmitted: (value) {
            if (value.trim().isNotEmpty) {
              setZoneCustomName(selectedZoneIndex.value, value.trim());
              Get.back();
            }
          },
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              if (isDefaultZoneName(currentZone.name)) {
                resetZoneToDefaultName(selectedZoneIndex.value);
                Get.back();
              }
            },
            child: const Text('Reset to Default'),
          ),
          ElevatedButton(
            onPressed: () {
              final newName = textController.text.trim();
              if (newName.isNotEmpty) {
                setZoneCustomName(selectedZoneIndex.value, newName);
                Get.back();
              }
            },
            child: const Text('Save'),
          ),
        ],
      ),
    );
  }

  @override
  void onInit() {
    super.onInit();

    // Listen to changes in the shape controller's knitting zones
    ever(shapeController.knittingInstructionsManager.knittingZones,
        (_) => update());

    // Listen to our own state changes to ensure UI updates
    ever(isCreatingNewZone, (_) => update());
    ever(isEditingMode, (_) => update());
    ever(selectedZoneIndex, (_) => update());

    // Ensure zones are properly ordered when controller initializes
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final currentZones =
          shapeController.knittingInstructionsManager.knittingZones.value;
      if (currentZones.isNotEmpty) {
        debugPrint('ZonesEditorController: Ensuring initial zone ordering...');
        _updateZoneOrder();

        // Create initial history entry for the loaded zones
        addZoneHistoryEntry('Initial Zone State');
      }
    });
  }
}
