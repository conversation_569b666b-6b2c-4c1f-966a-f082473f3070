import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:xoxknit/app/modules/shape_test/controllers/managers/knitting_instructions_manager.dart';
import 'package:xoxknit/app/modules/new_item/controllers/new_item_wizard_controller.dart';
import 'package:xoxknit/app/modules/new_item/utils/zone_configuration_processor.dart';

class KnittingZoneControlWidget extends StatelessWidget {
  final KnittingZone zone;
  final int index;
  final VoidCallback? onPreviousZone;
  final VoidCallback? onNextZone;
  final bool isSelected;

  const KnittingZoneControlWidget({
    super.key,
    required this.zone,
    required this.index,
    this.onPreviousZone,
    this.onNextZone,
    this.isSelected = false,
  });

  @override
  Widget build(BuildContext context) {
    final isEmptyZone = zone.config.value.isEmpty.value;

    return Card(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      elevation: isSelected ? 5 : 2,
      color: isSelected ? Colors.white : null,
      child: Container(
        decoration: isSelected
            ? BoxDecoration(
                borderRadius: BorderRadius.circular(16),
                border: Border.all(
                  color: Colors.blue.shade300,
                  width: 2,
                ),
              )
            : null,
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Different UI based on zone type
            if (isEmptyZone)
              _buildEmptyZoneControl(context)
            else
              _buildRegularZoneControl(context),
          ],
        ),
      ),
    );
  }

  Widget _buildRegularZoneControl(BuildContext context) {
    final wizardController = Get.find<NewItemWizardController>();

    return Column(
      children: [
        // Section 1: Auto control carriage position
        _buildControlSection(
          title: 'knittingZone_autoControlCarriagePosition'.tr,
          toggleValue: zone.config.value.autoControlCarriagePosition,
          onToggleChanged: (value) {
            zone.config.update((config) {
              config?.autoControlCarriagePosition.value = value;
            });
          },
          additionalControls: Obx(() {
            final isEnabled =
                !zone.config.value.autoControlCarriagePosition.value;
            return _buildToggleOption(
              label: 'knittingZone_startWithCarriageOn'.tr,
              leftOption: 'knittingZone_left'.tr,
              rightOption: 'knittingZone_right'.tr,
              value: zone.config.value.carriagePosition.value == 'right',
              isEnabled: isEnabled,
              onChanged: (value) {
                if (isEnabled) {
                  zone.config.update((config) {
                    config?.carriagePosition.value = value ? 'right' : 'left';
                  });
                }
              },
            );
          }),
        ),

        const SizedBox(height: 8),

        // Section 2: Auto adjust slightly asymmetrical rows
        _buildControlSection(
          title: 'knittingZone_autoAdjustAsymmetricalRows'.tr,
          toggleValue: zone.config.value.autoAdjustAsymmetricalRows,
          onToggleChanged: (value) {
            zone.config.update((config) {
              config?.autoAdjustAsymmetricalRows.value = value;
            });
          },
          additionalControls: Obx(() {
            final isEnabled =
                !zone.config.value.autoAdjustAsymmetricalRows.value;
            return _buildToggleOption(
              label: 'knittingZone_byDecreasingOrIncreasing'.tr,
              leftOption: 'knittingZone_dec'.tr,
              rightOption: 'knittingZone_inc'.tr,
              value: zone.config.value.asymmetricalDirection.value == 'inc',
              isEnabled: isEnabled,
              onChanged: (value) {
                if (isEnabled) {
                  zone.config.update((config) {
                    config?.asymmetricalDirection.value = value ? 'inc' : 'dec';
                  });
                }
              },
            );
          }),
        ),

        const SizedBox(height: 8),

        // Section 3: Auto control increases and decreases
        _buildControlSection(
          title: 'knittingZone_autoControlIncreaseDecrease'.tr,
          toggleValue: zone.config.value.autoControlIncreaseDecrease,
          onToggleChanged: (value) {
            zone.config.update((config) {
              config?.autoControlIncreaseDecrease.value = value;
            });
          },
          additionalControls: Obx(() {
            final isEnabled =
                !zone.config.value.autoControlIncreaseDecrease.value;
            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Increase controls
                Padding(
                  padding: const EdgeInsets.only(top: 6.0),
                  child: _buildInputRow(
                    label: 'knittingZone_increaseBy'.tr,
                    value: zone.config.value.increaseBy.toString(),
                    suffix: 'knittingZone_stitchesEvery'.tr,
                    secondValue: zone.config.value.increaseEvery.toString(),
                    secondSuffix: 'knittingZone_rows'.tr,
                    isEnabled: isEnabled,
                    onFirstValueChanged: (value) {
                      if (isEnabled) {
                        final intValue = int.tryParse(value) ?? 0;
                        zone.config.update((config) {
                          config?.increaseBy.value = intValue;
                        });
                      }
                    },
                    onSecondValueChanged: (value) {
                      if (isEnabled) {
                        final intValue = int.tryParse(value) ?? 0;
                        zone.config.update((config) {
                          config?.increaseEvery.value = intValue;
                        });
                      }
                    },
                  ),
                ),

                // Decrease controls
                Padding(
                  padding: const EdgeInsets.only(top: 6.0),
                  child: _buildInputRow(
                    label: 'knittingZone_decreaseBy'.tr,
                    value: zone.config.value.decreaseBy.toString(),
                    suffix: 'knittingZone_stitchesEvery'.tr,
                    secondValue: zone.config.value.decreaseEvery.toString(),
                    secondSuffix: 'knittingZone_rows'.tr,
                    isEnabled: isEnabled,
                    onFirstValueChanged: (value) {
                      if (isEnabled) {
                        final intValue = int.tryParse(value) ?? 0;
                        zone.config.update((config) {
                          config?.decreaseBy.value = intValue;
                        });
                      }
                    },
                    onSecondValueChanged: (value) {
                      if (isEnabled) {
                        final intValue = int.tryParse(value) ?? 0;
                        zone.config.update((config) {
                          config?.decreaseEvery.value = intValue;
                        });
                      }
                    },
                  ),
                ),
              ],
            );
          }),
        ),

        const SizedBox(height: 16),

        // Use the new navigation buttons widget
        _buildNavigationButtons(
          // Back action
          () {
            wizardController.saveKnittingZoneConfigurations();
            if (onPreviousZone != null) {
              onPreviousZone!();
            }
          },
          // Apply action
          () {
            wizardController.applyZoneConfigToAll(zone);
            wizardController.saveKnittingZoneConfigurations();
            Get.snackbar(
              'knittingZone_settingsApplied'.tr,
              'knittingZone_settingsFromApplied'
                  .trParams({'zoneName': zone.name}),
              snackPosition: SnackPosition.BOTTOM,
              backgroundColor: Colors.green,
              colorText: Colors.white,
            );
          },
          // Next action
          () {
            wizardController.saveKnittingZoneConfigurations();
            if (onNextZone != null) {
              onNextZone!();
            }
          },
        ),
      ],
    );
  }

  Widget _buildControlSection({
    required String title,
    required RxBool toggleValue,
    required Function(bool) onToggleChanged,
    required Widget additionalControls,
  }) {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: Colors.grey.shade200,
        borderRadius: BorderRadius.circular(16),
      ),
      padding: const EdgeInsets.all(12),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildToggleRow(
            title: title,
            value: toggleValue,
            onChanged: onToggleChanged,
          ),
          additionalControls,
        ],
      ),
    );
  }

  Widget _buildToggleRow({
    required String title,
    required RxBool value,
    required Function(bool) onChanged,
  }) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Flexible(
          flex: 2,
          child: Text(title,
              style: TextStyle(fontSize: 14, fontWeight: FontWeight.w500)),
        ),
        Obx(() => SizedBox(
              width: 100,
              child: _buildSwitch(
                value: value.value,
                onChanged: onChanged,
              ),
            )),
      ],
    );
  }

  Widget _buildSwitch({
    required bool value,
    required Function(bool) onChanged,
  }) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        Text('knittingZone_off'.tr, style: TextStyle(fontSize: 12)),
        Transform.scale(
          scale: 0.7,
          child: Switch(
            value: value,
            onChanged: onChanged,
            activeColor: Colors.green,
            trackColor: WidgetStateProperty.resolveWith((states) =>
                states.contains(WidgetState.selected)
                    ? Colors.green.withOpacity(0.5)
                    : Colors.grey.shade400),
            materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
          ),
        ),
        Text('knittingZone_on'.tr, style: TextStyle(fontSize: 12)),
      ],
    );
  }

  Widget _buildToggleOption({
    required String label,
    required String leftOption,
    required String rightOption,
    required bool value,
    required bool isEnabled,
    required Function(bool) onChanged,
  }) {
    return Padding(
      padding: const EdgeInsets.only(top: 8.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Flexible(
            flex: 3,
            child: Text(
              label,
              style: TextStyle(fontSize: 13),
              overflow: TextOverflow.ellipsis,
              maxLines: 2,
            ),
          ),
          SizedBox(
            width: 120,
            child: Row(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                Flexible(
                  child: Text(
                    leftOption,
                    style: TextStyle(fontSize: 12),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                Transform.scale(
                  scale: 0.7,
                  child: Switch(
                    value: value,
                    onChanged: isEnabled ? onChanged : null,
                    activeColor: Colors.green,
                    inactiveThumbColor: Colors.grey,
                    trackColor: WidgetStateProperty.resolveWith((states) {
                      if (!isEnabled) return Colors.grey.shade300;
                      return states.contains(WidgetState.selected)
                          ? Colors.green.withValues(alpha: .5)
                          : Colors.grey.shade400;
                    }),
                    materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                  ),
                ),
                Flexible(
                  child: Text(
                    rightOption,
                    style: TextStyle(fontSize: 12),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInputRow({
    required String label,
    required String value,
    required String suffix,
    required String secondValue,
    required String secondSuffix,
    required bool isEnabled,
    required Function(String) onFirstValueChanged,
    required Function(String) onSecondValueChanged,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 6.0),
      child: Wrap(
        alignment: WrapAlignment.spaceBetween,
        crossAxisAlignment: WrapCrossAlignment.center,
        spacing: 6,
        runSpacing: 8,
        children: [
          // First row elements
          Text(
            label,
            style: TextStyle(fontSize: 12),
            overflow: TextOverflow.ellipsis,
          ),
          SizedBox(
            width: 45,
            height: 35,
            child: TextField(
              decoration: InputDecoration(
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(24),
                  borderSide: BorderSide.none,
                ),
                contentPadding:
                    const EdgeInsets.symmetric(horizontal: 10, vertical: 8),
                filled: true,
                fillColor: Colors.white,
              ),
              textAlign: TextAlign.center,
              keyboardType: TextInputType.number,
              controller: TextEditingController(text: value),
              enabled: isEnabled,
              onChanged: onFirstValueChanged,
              style: TextStyle(fontSize: 13),
            ),
          ),

          Text(
            suffix,
            style: TextStyle(fontSize: 12),
            overflow: TextOverflow.ellipsis,
          ),

          SizedBox(
            width: 45,
            height: 35,
            child: TextField(
              decoration: InputDecoration(
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(24),
                  borderSide: BorderSide.none,
                ),
                contentPadding:
                    const EdgeInsets.symmetric(horizontal: 10, vertical: 8),
                filled: true,
                fillColor: Colors.white,
              ),
              textAlign: TextAlign.center,
              keyboardType: TextInputType.number,
              controller: TextEditingController(text: secondValue),
              enabled: isEnabled,
              onChanged: onSecondValueChanged,
              style: TextStyle(fontSize: 13),
            ),
          ),

          Text(
            secondSuffix,
            style: TextStyle(fontSize: 12),
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }

  Widget _buildButton({
    required String label,
    required Color color,
    Color textColor = Colors.black,
    required VoidCallback onPressed,
    bool isCompact = false,
  }) {
    // For compact buttons, use a different layout with smaller dimensions
    if (isCompact) {
      return SizedBox(
        height: 32,
        child: Tooltip(
          message: label,
          child: ElevatedButton(
            onPressed: onPressed,
            style: ElevatedButton.styleFrom(
              backgroundColor: color,
              foregroundColor: textColor,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16),
              ),
              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 0),
              minimumSize: Size.zero, // Allows button to be more compact
              tapTargetSize: MaterialTapTargetSize.shrinkWrap,
            ),
            child: Text(
              label,
              textAlign: TextAlign.center,
              style: TextStyle(fontSize: 10, fontWeight: FontWeight.bold),
              overflow: TextOverflow.ellipsis,
              maxLines: 1,
            ),
          ),
        ),
      );
    }

    // Regular buttons remain the same
    return SizedBox(
      height: 40,
      child: Tooltip(
        message: label,
        child: ElevatedButton(
          onPressed: onPressed,
          style: ElevatedButton.styleFrom(
            backgroundColor: color,
            foregroundColor: textColor,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(20),
            ),
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          ),
          child: Text(
            label,
            textAlign: TextAlign.center,
            style: TextStyle(fontSize: 13),
            overflow: TextOverflow.ellipsis,
            maxLines: 2,
          ),
        ),
      ),
    );
  }

  Widget _buildNavigationButtons(VoidCallback backAction,
      VoidCallback applyAction, VoidCallback nextAction,
      {bool showApplyButton = true}) {
    return SizedBox(
      width: double.infinity,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          // Back button
          Expanded(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 2),
              child: _buildButton(
                label: 'knittingZone_back'.tr,
                color: Colors.grey.shade400,
                onPressed: backAction,
                isCompact: true,
              ),
            ),
          ),

          // Apply button - only show if requested
          if (showApplyButton)
            Expanded(
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 2),
                child: _buildButton(
                  label: 'knittingZone_applyToAll'.tr,
                  color: Colors.blue,
                  textColor: Colors.white,
                  onPressed: applyAction,
                  isCompact: true,
                ),
              ),
            ),

          // Next button
          Expanded(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 2),
              child: _buildButton(
                label: 'knittingZone_next'.tr,
                color: Colors.green,
                textColor: Colors.white,
                onPressed: nextAction,
                isCompact: true,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyZoneControl(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Finishing method for empty zone
        Text(
          'knittingZone_finishingMethodForBottomEdge'.tr,
          style: TextStyle(fontSize: 14),
          overflow: TextOverflow.ellipsis,
          maxLines: 2,
        ),
        const SizedBox(height: 8),

        // Radio buttons for finishing method
        Obx(() {
          return Wrap(
            spacing: 16,
            runSpacing: 8,
            children: [
              _buildButton(
                label: 'knittingZone_bindOff'.tr,
                color: zone.config.value.finishingMethod.value == 'bind off'
                    ? Colors.green
                    : Colors.grey.shade300,
                textColor: zone.config.value.finishingMethod.value == 'bind off'
                    ? Colors.white
                    : Colors.black,
                onPressed: () {
                  zone.config.update((config) {
                    config?.finishingMethod.value = 'bind off';
                  });
                },
              ),
              _buildButton(
                label: 'knittingZone_useScrap'.tr,
                color: zone.config.value.finishingMethod.value == 'use scrap'
                    ? Colors.green
                    : Colors.grey.shade300,
                textColor:
                    zone.config.value.finishingMethod.value == 'use scrap'
                        ? Colors.white
                        : Colors.black,
                onPressed: () {
                  zone.config.update((config) {
                    config?.finishingMethod.value = 'use scrap';
                  });
                },
              ),
            ],
          );
        }),

        const SizedBox(height: 16),

        // Use the new navigation buttons widget without Apply button
        _buildNavigationButtons(
          // Back action
          () {
            if (onPreviousZone != null) {
              onPreviousZone!();
            }
          },
          // Apply action (not used)
          () {},
          // Next action
          () {
            if (onNextZone != null) {
              onNextZone!();
            }
          },
          showApplyButton: false,
        ),
      ],
    );
  }
}

class KnittingZonePainter extends CustomPainter {
  final KnittingZone zone;
  final double aspectRatio; // Aspect ratio of stitches (width / height)

  KnittingZonePainter({
    required this.zone,
    this.aspectRatio =
        0.75, // Default aspect ratio for knitting stitches (typical gauge)
  });

  @override
  void paint(Canvas canvas, Size size) {
    if (zone.instructions.isEmpty) return;

    // Apply zone configurations before visualizing
    final processedInstructions =
        ZoneConfigurationProcessor.applyZoneConfiguration(zone);

    if (processedInstructions.isEmpty) return;

    // Get trimmed instructions that remove padding spaces from the processed instructions
    final trimmedData = _getTrimmedInstructions(processedInstructions);
    final trimmedInstructions = trimmedData['instructions'] as List<List<bool>>;

    if (trimmedInstructions.isEmpty) return;

    final paint = Paint()
      ..color = Colors.black
      ..style = PaintingStyle.fill;

    final highlightPaint = Paint()
      ..color = Colors.blue.shade300
      ..style = PaintingStyle.stroke
      ..strokeWidth = 0.5;

    // Calculate the available drawing area size
    final availableWidth = size.width;
    final availableHeight = size.height;

    // Calculate grid dimensions based on content and aspect ratio
    final contentWidth = trimmedInstructions[0].length;
    final contentHeight = trimmedInstructions.length;

    // Correct the interpretation of aspectRatio
    // The passed aspectRatio is stitchesPerCm / rowsPerCm (effectively widthUnit / heightUnit)
    // The painter's logic below expects cellWidth / cellHeight.
    // To match physical stitches, cellWidth/cellHeight should be (1/stitchesPerCm) / (1/rowsPerCm) = rowsPerCm / stitchesPerCm.
    // So, the internalAspectRatio for the painter's logic should be 1.0 / this.aspectRatio.
    final double internalAspectRatio = aspectRatio != 0
        ? 1.0 / aspectRatio
        : 1.0; // Default to 1.0 if aspectRatio is 0

    // Calculate cell size respecting aspect ratio
    double cellWidth;
    double cellHeight;

    // Determine if width or height is the limiting factor using the corrected internalAspectRatio
    final widthLimited = availableWidth / contentWidth <
        (availableHeight / contentHeight) *
            internalAspectRatio; // Use internalAspectRatio

    if (widthLimited) {
      // Width is the limiting factor
      cellWidth = availableWidth / contentWidth;
      cellHeight = cellWidth / internalAspectRatio; // Use internalAspectRatio
    } else {
      // Height is the limiting factor
      cellHeight = availableHeight / contentHeight;
      cellWidth = cellHeight * internalAspectRatio; // Use internalAspectRatio
    }

    // Calculate the drawing offset to center the content
    final offsetX = (availableWidth - (contentWidth * cellWidth)) / 2;
    final offsetY = (availableHeight - (contentHeight * cellHeight)) / 2;

    // Draw grid
    final gridPaint = Paint()
      ..color = Colors.grey.shade200
      ..style = PaintingStyle.stroke
      ..strokeWidth = 0.3;

    for (int row = 0; row <= contentHeight; row++) {
      final y = offsetY + (row * cellHeight);
      canvas.drawLine(
        Offset(offsetX, y),
        Offset(offsetX + (contentWidth * cellWidth), y),
        gridPaint,
      );
    }

    for (int col = 0; col <= contentWidth; col++) {
      final x = offsetX + (col * cellWidth);
      canvas.drawLine(
        Offset(x, offsetY),
        Offset(x, offsetY + (contentHeight * cellHeight)),
        gridPaint,
      );
    }

    // Draw the actual stitches
    for (int row = 0; row < trimmedInstructions.length; row++) {
      for (int col = 0; col < trimmedInstructions[row].length; col++) {
        if (trimmedInstructions[row][col]) {
          final rect = Rect.fromLTWH(
            offsetX + (col * cellWidth),
            offsetY + (row * cellHeight),
            cellWidth,
            cellHeight,
          );

          // Fill the stitch
          canvas.drawRect(rect, paint);

          // Add a subtle highlight for better visibility
          canvas.drawRect(rect, highlightPaint);
        }
      }
    }
  }

  /// Helper method to get trimmed instructions (removing padding false values)
  Map<String, dynamic> _getTrimmedInstructions(List<List<bool>> instructions) {
    if (instructions.isEmpty) {
      return {
        'instructions': <List<bool>>[],
        'leftTrim': 0,
        'rightTrim': 0,
        'topTrim': 0,
        'bottomTrim': 0,
      };
    }

    int leftTrim = instructions[0].length;
    int rightTrim = 0;
    int topTrim = 0;
    int bottomTrim = 0;

    // Find the bounding box of all true values
    bool foundContent = false;
    int firstContentRow = -1;
    int lastContentRow = -1;

    for (int row = 0; row < instructions.length; row++) {
      bool rowHasContent = false;
      for (int col = 0; col < instructions[row].length; col++) {
        if (instructions[row][col]) {
          rowHasContent = true;
          foundContent = true;
          leftTrim = leftTrim < col ? leftTrim : col;
          rightTrim = rightTrim > col ? rightTrim : col;
        }
      }
      if (rowHasContent) {
        if (firstContentRow == -1) firstContentRow = row;
        lastContentRow = row;
      }
    }

    if (!foundContent) {
      return {
        'instructions': <List<bool>>[],
        'leftTrim': 0,
        'rightTrim': 0,
        'topTrim': 0,
        'bottomTrim': 0,
      };
    }

    // Calculate trims
    topTrim = firstContentRow;
    bottomTrim = instructions.length - 1 - lastContentRow;
    final rightPadding = instructions[0].length - 1 - rightTrim;

    // Create trimmed instructions
    final List<List<bool>> trimmed = [];
    for (int row = firstContentRow; row <= lastContentRow; row++) {
      final trimmedRow = instructions[row].sublist(leftTrim, rightTrim + 1);
      trimmed.add(trimmedRow);
    }

    return {
      'instructions': trimmed,
      'leftTrim': leftTrim,
      'rightTrim': rightPadding,
      'topTrim': topTrim,
      'bottomTrim': bottomTrim,
    };
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    if (oldDelegate is KnittingZonePainter) {
      // Always repaint when zone config changes
      return true;
    }
    return true;
  }
}
